import*as r from"react";function n(){return n=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},n.apply(this,arguments)}var e=["children","options"],t={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},u=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(r,n){return r[n.toLowerCase()]=n,r},{class:"className",for:"htmlFor"}),a={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},i=["style","script","pre"],o=["src","href","data","formAction","srcDoc","action"],c=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,f=/mailto:/i,l=/\n{2,}$/,_=/^(\s*>[\s\S]*?)(?=\n\n|$)/,d=/^ *> ?/gm,s=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,p=/^ {2,}\n/,v=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,y=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,h=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,g=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,m=/^(?:\n *)*\n/,k=/\r\n?/g,x=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,q=/^\[\^([^\]]+)]/,b=/\f/g,S=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,z=/^\s*?\[(x|\s)\]/,$=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,E=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^([^\n]+)\n *(=|-){3,} *\n/,R=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,B=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,L=/^<!--[\s\S]*?(?:-->)/,O=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,j=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,C=/^\{.*\}$/,I=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,T=/^<([^ >]+@[^ >]+)>/,M=/^<([^ >]+:\/[^ >]+)>/,w=/-([a-z])?/gi,D=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,F=/^[^\n]+(?:  \n|\n{2,})/,P=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,Z=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,N=/^\[([^\]]*)\] ?\[([^\]]*)\]/,G=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,U=/\t/g,V=/(^ *\||\| *$)/g,H=/^ *:-+: *$/,Q=/^ *:-+ *$/,W=/^ *-+: *$/,J="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",K=RegExp("^([*_])\\1"+J+"\\1\\1(?!\\1)"),X=RegExp("^([*_])"+J+"\\1(?!\\1)"),Y=RegExp("^(==)"+J+"\\1"),rr=RegExp("^(~~)"+J+"\\1"),nr=/^(:[a-zA-Z0-9-_]+:)/,er=/^\\([^0-9A-Za-z\s])/,tr=/\\([^0-9A-Za-z\s])/g,ur=/^[\s\S](?:(?!  \n|[0-9]\.|http)[^=*_~\-\n:<`\\\[!])*/,ar=/^\n+/,ir=/^([ \t]*)/,or=/(?:^|\n)( *)$/,cr="(?:\\d+\\.)",fr="(?:[*+-])";function lr(r){return"( *)("+(1===r?cr:fr)+") +"}var _r=lr(1),dr=lr(2);function sr(r){return RegExp("^"+(1===r?_r:dr))}var pr=sr(1),vr=sr(2);function yr(r){return RegExp("^"+(1===r?_r:dr)+"[^\\n]*(?:\\n(?!\\1"+(1===r?cr:fr)+" )[^\\n]*)*(\\n|$)","gm")}var hr=yr(1),gr=yr(2);function mr(r){var n=1===r?cr:fr;return RegExp("^( *)("+n+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+n+" (?!"+n+" ))\\n*|\\s*\\n*$)")}var kr=mr(1),xr=mr(2);function qr(r,n){var e=1===n,t=e?kr:xr,u=e?hr:gr,a=e?pr:vr;return{t:function(r){return a.test(r)},u:jr(function(r,n){var e=or.exec(n.prevCapture);return e&&(n.list||!n.inline&&!n.simple)?t.exec(r=e[1]+r):null}),i:1,o:function(r,n,t){var i=e?+r[2]:void 0,o=r[0].replace(l,"\n").match(u),c=!1;return{items:o.map(function(r,e){var u=a.exec(r)[0].length,i=RegExp("^ {1,"+u+"}","gm"),f=r.replace(i,"").replace(a,""),l=e===o.length-1,_=-1!==f.indexOf("\n\n")||l&&c;c=_;var d,s=t.inline,p=t.list;t.list=!0,_?(t.inline=!1,d=zr(f)+"\n\n"):(t.inline=!0,d=zr(f));var v=n(d,t);return t.inline=s,t.list=p,v}),ordered:e,start:i}},l:function(n,e,t){return r(n.ordered?"ol":"ul",{key:t.key,start:"20"===n.type?n.start:void 0},n.items.map(function(n,u){return r("li",{key:u},e(n,t))}))}}}var br=RegExp("^\\[((?:\\[[^\\[\\]]*(?:\\[[^\\[\\]]*\\][^\\[\\]]*)*\\]|[^\\[\\]])*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Sr=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;function zr(r){for(var n=r.length;n>0&&r[n-1]<=" ";)n--;return r.slice(0,n)}function $r(r,n){return r.startsWith(n)}function Er(r,n,e){if(Array.isArray(e)){for(var t=0;t<e.length;t++)if($r(r,e[t]))return!0;return!1}return e(r,n)}function Ar(r){return r.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Rr(r){return W.test(r)?"right":H.test(r)?"center":Q.test(r)?"left":null}function Br(r,n,e,t){var u=e.inTable;e.inTable=!0;var a=[[]],i="";function o(){if(i){var r=a[a.length-1];r.push.apply(r,n(i,e)),i=""}}return r.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(r,n,e){"|"===r.trim()&&(o(),t)?0!==n&&n!==e.length-1&&a.push([]):i+=r}),o(),e.inTable=u,a}function Lr(r,n,e){e.inline=!0;var t=r[2]?r[2].replace(V,"").split("|").map(Rr):[],u=r[3]?function(r,n,e){return r.trim().split("\n").map(function(r){return Br(r,n,e,!0)})}(r[3],n,e):[],a=Br(r[1],n,e,!!u.length);return e.inline=!1,u.length?{align:t,cells:u,header:a,type:"25"}:{children:a,type:"21"}}function Or(r,n){return null==r.align[n]?{}:{textAlign:r.align[n]}}function jr(r){return r.inline=1,r}function Cr(r){return jr(function(n,e){return e.inline?r.exec(n):null})}function Ir(r){return jr(function(n,e){return e.inline||e.simple?r.exec(n):null})}function Tr(r){return function(n,e){return e.inline||e.simple?null:r.exec(n)}}function Mr(r){return jr(function(n){return r.exec(n)})}var wr=/(javascript|vbscript|data(?!:image)):/i;function Dr(r){try{var n=decodeURIComponent(r).replace(/[^A-Za-z0-9/:]/g,"");if(wr.test(n))return null}catch(r){return null}return r}function Fr(r){return r?r.replace(tr,"$1"):r}function Pr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!0,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Zr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!1,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Nr(r,n,e){var t=e.inline||!1;e.inline=!1;var u=r(n,e);return e.inline=t,u}var Gr=function(r,n,e){return{children:Pr(n,r[2],e)}};function Ur(){return{}}function Vr(){return null}function Hr(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Qr(r,n,e){for(var t=r,u=n.split(".");u.length&&void 0!==(t=t[u[0]]);)u.shift();return t||e}function Wr(r,n){var e=Qr(n,r);return e?"function"==typeof e||"object"==typeof e&&"render"in e?e:Qr(n,r+".component",r):r}function Jr(e,t){var l;void 0===e&&(e=""),void 0===t&&(t={}),t.overrides=t.overrides||{},t.namedCodesToUnicode=t.namedCodesToUnicode?n({},a,t.namedCodesToUnicode):a;var V=t.slugify||Ar,H=t.sanitizer||Dr,Q=t.createElement||r.createElement,W=[_,y,h,t.enforceAtxHeadings?E:$,A,D,kr,xr],J=[].concat(W,[F,R,L,j]);function tr(r,e){var u=Qr(t.overrides,r+".props",{});return Q.apply(void 0,[Wr(r,t.overrides),n({},e,u,{className:Hr(null==e?void 0:e.className,u.className)||void 0})].concat([].slice.call(arguments,2)))}function or(r){r=r.replace(S,"");var n=!1;t.forceInline?n=!0:t.forceBlock||(n=!1===G.test(r));for(var e=pr(sr(n?r:zr(r).replace(ar,"")+"\n\n",{inline:n}));"string"==typeof e[e.length-1]&&!e[e.length-1].trim();)e.pop();if(null===t.wrapper)return e;var u,a=t.wrapper||(n?"span":"div");if(e.length>1||t.forceWrapper)u=e;else{if(1===e.length)return"string"==typeof(u=e[0])?tr("span",{key:"outer"},u):u;u=null}return Q(a,{key:"outer"},u)}function cr(r,n){if(!n||!n.trim())return null;var e=n.match(c);return e?e.reduce(function(n,e){var t=e.indexOf("=");if(-1!==t){var a=function(r){return-1!==r.indexOf("-")&&null===r.match(O)&&(r=r.replace(w,function(r,n){return n.toUpperCase()})),r}(e.slice(0,t)).trim(),i=function(r){var n=r[0];return('"'===n||"'"===n)&&r.length>=2&&r[r.length-1]===n?r.slice(1,-1):r}(e.slice(t+1).trim()),c=u[a]||a;if("ref"===c)return n;var f=n[c]=function(r,n,e,t){return"style"===n?function(r){var n=[],e="",t=!1,u=!1,a="";if(!r)return n;for(var i=0;i<r.length;i++){var o=r[i];if('"'!==o&&"'"!==o||t||(u?o===a&&(u=!1,a=""):(u=!0,a=o)),"("===o&&e.endsWith("url")?t=!0:")"===o&&t&&(t=!1),";"!==o||u||t)e+=o;else{var c=e.trim();if(c){var f=c.indexOf(":");if(f>0){var l=c.slice(0,f).trim(),_=c.slice(f+1).trim();n.push([l,_])}}e=""}}var d=e.trim();if(d){var s=d.indexOf(":");if(s>0){var p=d.slice(0,s).trim(),v=d.slice(s+1).trim();n.push([p,v])}}return n}(e).reduce(function(n,e){var u=e[0],a=e[1];return n[u.replace(/(-[a-z])/g,function(r){return r[1].toUpperCase()})]=t(a,r,u),n},{}):-1!==o.indexOf(n)?t(Fr(e),r,n):(e.match(C)&&(e=Fr(e.slice(1,e.length-1))),"true"===e||"false"!==e&&e)}(r,a,i,H);"string"==typeof f&&(R.test(f)||j.test(f))&&(n[c]=or(f.trim()))}else"style"!==e&&(n[u[e]||e]=!0);return n},{}):null}var fr=[],lr={},_r=((l={})[0]={t:[">"],u:Tr(_),i:1,o:function(r,n,e){var t=r[0].replace(d,"").match(s);return{alert:t[1],children:n(t[2],e)}},l:function(r,n,e){var t={key:e.key};return r.alert&&(t.className="markdown-alert-"+V(r.alert.toLowerCase(),Ar),r.children.unshift({attrs:{},children:[{type:"27",text:r.alert}],noInnerParse:!0,type:"11",tag:"header"})),tr("blockquote",t,n(r.children,e))}},l[1]={u:Mr(p),i:1,o:Ur,l:function(r,n,e){return tr("br",{key:e.key})}},l[2]={t:function(r){var n=r[0];return"-"===n||"*"===n||"_"===n},u:Tr(v),i:1,o:Ur,l:function(r,n,e){return tr("hr",{key:e.key})}},l[3]={t:["    "],u:Tr(h),i:0,o:function(r){return{lang:void 0,text:Fr(zr(r[0].replace(/^ {4}/gm,"")))}},l:function(r,e,t){return tr("pre",{key:t.key},tr("code",n({},r.attrs,{className:r.lang?"lang-"+r.lang:""}),r.text))}},l[4]={t:["```","~~~"],u:Tr(y),i:0,o:function(r){return{attrs:cr("code",r[3]||""),lang:r[2]||void 0,text:r[4],type:"3"}}},l[5]={t:["`"],u:Ir(g),i:3,o:function(r){return{text:Fr(r[2])}},l:function(r,n,e){return tr("code",{key:e.key},r.text)}},l[6]={t:["[^"],u:Tr(x),i:0,o:function(r){return fr.push({footnote:r[2],identifier:r[1]}),{}},l:Vr},l[7]={t:["[^"],u:Cr(q),i:1,o:function(r){return{target:"#"+V(r[1],Ar),text:r[1]}},l:function(r,n,e){return tr("a",{key:e.key,href:H(r.target,"a","href")},tr("sup",{key:e.key},r.text))}},l[8]={t:["[ ]","[x]"],u:Cr(z),i:1,o:function(r){return{completed:"x"===r[1].toLowerCase()}},l:function(r,n,e){return tr("input",{checked:r.completed,key:e.key,readOnly:!0,type:"checkbox"})}},l[9]={t:["#"],u:Tr(t.enforceAtxHeadings?E:$),i:1,o:function(r,n,e){return{children:Pr(n,r[2],e),id:V(r[2],Ar),level:r[1].length}},l:function(r,n,e){return tr("h"+r.level,{id:r.id,key:e.key},n(r.children,e))}},l[10]={u:Tr(A),i:0,o:function(r,n,e){return{children:Pr(n,r[1],e),level:"="===r[2]?1:2,type:"9"}}},l[11]={t:["<"],u:Mr(R),i:1,o:function(r,n,e){var t,u=r[3].match(ir),a=RegExp("^"+u[1],"gm"),o=r[3].replace(a,""),c=(t=o,J.some(function(r){return r.test(t)})?Nr:Pr),f=r[1].toLowerCase(),l=-1!==i.indexOf(f),_=(l?f:r[1]).trim(),d={attrs:cr(_,r[2]),noInnerParse:l,tag:_};if(e.inAnchor=e.inAnchor||"a"===f,l)d.text=r[3];else{var s=e.inHTML;e.inHTML=!0,d.children=c(n,o,e),e.inHTML=s}return e.inAnchor=!1,d},l:function(r,e,t){return tr(r.tag,n({key:t.key},r.attrs),r.text||(r.children?e(r.children,t):""))}},l[13]={t:["<"],u:Mr(j),i:1,o:function(r){var n=r[1].trim();return{attrs:cr(n,r[2]||""),tag:n}},l:function(r,e,t){return tr(r.tag,n({},r.attrs,{key:t.key}))}},l[12]={t:["\x3c!--"],u:Mr(L),i:1,o:function(){return{}},l:Vr},l[14]={t:["!["],u:Ir(Sr),i:1,o:function(r){return{alt:Fr(r[1]),target:Fr(r[2]),title:Fr(r[3])}},l:function(r,n,e){return tr("img",{key:e.key,alt:r.alt||void 0,title:r.title||void 0,src:H(r.target,"img","src")})}},l[15]={t:["["],u:Cr(br),i:3,o:function(r,n,e){return{children:Zr(n,r[1],e),target:Fr(r[2]),title:Fr(r[3])}},l:function(r,n,e){return tr("a",{key:e.key,href:H(r.target,"a","href"),title:r.title},n(r.children,e))}},l[16]={t:["<"],u:Cr(M),i:0,o:function(r){return{children:[{text:r[1],type:"27"}],target:r[1],type:"15"}}},l[17]={t:function(r,n){return!n.inAnchor&&!t.disableAutoLink&&($r(r,"http://")||$r(r,"https://"))},u:Cr(I),i:0,o:function(r){return{children:[{text:r[1],type:"27"}],target:r[1],title:void 0,type:"15"}}},l[18]={t:["<"],u:Cr(T),i:0,o:function(r){var n=r[1],e=r[1];return f.test(e)||(e="mailto:"+e),{children:[{text:n.replace("mailto:",""),type:"27"}],target:e,type:"15"}}},l[20]=qr(tr,1),l[33]=qr(tr,2),l[19]={u:Tr(m),i:3,o:Ur,l:function(){return"\n"}},l[21]={u:jr(function(r,n){if(n.inline||n.simple||n.inHTML&&-1===r.indexOf("\n\n")&&-1===n.prevCapture.indexOf("\n\n"))return null;var e="";r.split("\n").every(function(r){return r+="\n",!W.some(function(n){return n.test(r)})&&(e+=r,!!r.trim())});var t=zr(e);return""===t?null:[e,,t]}),i:3,o:Gr,l:function(r,n,e){return tr("p",{key:e.key},n(r.children,e))}},l[22]={t:["["],u:Cr(P),i:0,o:function(r){return lr[r[1]]={target:r[2],title:r[4]},{}},l:Vr},l[23]={t:["!["],u:Ir(Z),i:0,o:function(r){return{alt:r[1]?Fr(r[1]):void 0,ref:r[2]}},l:function(r,n,e){return lr[r.ref]?tr("img",{key:e.key,alt:r.alt,src:H(lr[r.ref].target,"img","src"),title:lr[r.ref].title}):null}},l[24]={t:function(r){return"["===r[0]&&-1===r.indexOf("](")},u:Cr(N),i:0,o:function(r,n,e){return{children:n(r[1],e),fallbackChildren:r[0],ref:r[2]}},l:function(r,n,e){return lr[r.ref]?tr("a",{key:e.key,href:H(lr[r.ref].target,"a","href"),title:lr[r.ref].title},n(r.children,e)):tr("span",{key:e.key},r.fallbackChildren)}},l[25]={t:["|"],u:Tr(D),i:1,o:Lr,l:function(r,n,e){var t=r;return tr("table",{key:e.key},tr("thead",null,tr("tr",null,t.header.map(function(r,u){return tr("th",{key:u,style:Or(t,u)},n(r,e))}))),tr("tbody",null,t.cells.map(function(r,u){return tr("tr",{key:u},r.map(function(r,u){return tr("td",{key:u,style:Or(t,u)},n(r,e))}))})))}},l[27]={u:jr(function(r,n){var e;return $r(r,":")&&(e=nr.exec(r)),e||ur.exec(r)}),i:4,o:function(r){var n=r[0];return{text:-1===n.indexOf("&")?n:n.replace(B,function(r,n){return t.namedCodesToUnicode[n]||r})}},l:function(r){return r.text}},l[28]={t:["**","__"],u:Ir(K),i:2,o:function(r,n,e){return{children:n(r[2],e)}},l:function(r,n,e){return tr("strong",{key:e.key},n(r.children,e))}},l[29]={t:function(r){var n=r[0];return("*"===n||"_"===n)&&r[1]!==n},u:Ir(X),i:3,o:function(r,n,e){return{children:n(r[2],e)}},l:function(r,n,e){return tr("em",{key:e.key},n(r.children,e))}},l[30]={t:["\\"],u:Ir(er),i:1,o:function(r){return{text:r[1],type:"27"}}},l[31]={t:["=="],u:Ir(Y),i:3,o:Gr,l:function(r,n,e){return tr("mark",{key:e.key},n(r.children,e))}},l[32]={t:["~~"],u:Ir(rr),i:3,o:Gr,l:function(r,n,e){return tr("del",{key:e.key},n(r.children,e))}},l);!0===t.disableParsingRawHTML&&(delete _r[11],delete _r[13]);var dr,sr=function(r){var n=Object.keys(r);function e(t,u){var a=[];if(u.prevCapture=u.prevCapture||"",t.trim())for(;t;)for(var i=0;i<n.length;){var o=n[i],c=r[o];if(!c.t||Er(t,u,c.t)){var f=c.u(t,u);if(f&&f[0]){t=t.substring(f[0].length);var l=c.o(f,e,u);u.prevCapture+=f[0],l.type||(l.type=o),a.push(l);break}i++}else i++}return u.prevCapture="",a}return n.sort(function(n,e){return r[n].i-r[e].i||(n<e?-1:1)}),function(r,n){return e(function(r){return r.replace(k,"\n").replace(b,"").replace(U,"    ")}(r),n)}}(_r),pr=(dr=function(r,n){return function(e,t,u){var a=r[e.type].l;return n?n(function(){return a(e,t,u)},e,t,u):a(e,t,u)}}(_r,t.renderRule),function r(n,e){if(void 0===e&&(e={}),Array.isArray(n)){for(var t=e.key,u=[],a=!1,i=0;i<n.length;i++){e.key=i;var o=r(n[i],e),c="string"==typeof o;c&&a?u[u.length-1]+=o:null!==o&&u.push(o),a=c}return e.key=t,u}return dr(n,r,e)}),vr=or(e);return fr.length?tr("div",null,vr,tr("footer",{key:"footer"},fr.map(function(r){return tr("div",{id:V(r.identifier,Ar),key:r.identifier},r.identifier,pr(sr(r.footnote,{inline:!0})))}))):vr}export default function(n){var t=n.children,u=void 0===t?"":t,a=n.options,i=function(r,n){if(null==r)return{};var e,t,u={},a=Object.keys(r);for(t=0;t<a.length;t++)n.indexOf(e=a[t])>=0||(u[e]=r[e]);return u}(n,e);return r.cloneElement(Jr(u,a),i)}export{t as RuleType,Jr as compiler,Dr as sanitizer,Ar as slugify};
//# sourceMappingURL=index.module.js.map
