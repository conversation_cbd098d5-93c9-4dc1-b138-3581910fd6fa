{"name": "markdown-to-jsx", "description": "Convert markdown to JSX with ease for React and React-like projects. Super lightweight and highly configurable.", "homepage": "https://markdown-to-jsx.quantizor.dev", "license": "MIT", "version": "7.7.12", "publishConfig": {"access": "public", "mangle": {"regex": "^_", "reserved": ["__html"], "compress": {"unsafe": true}}}, "engines": {"node": ">= 10"}, "keywords": ["markdown", "markdown converter", "react", "preact", "jsx", "html"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/quantizor/markdown-to-jsx.git"}, "bugs": "https://github.com/quantizor/markdown-to-jsx/issues", "files": ["dist", "LICENSE", "README.md"], "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.ts", "node": "./dist/index.module.js", "default": "./dist/index.modern.js"}, "require": {"types": "./dist/index.cjs.d.ts", "default": "./dist/index.cjs"}}}, "sideEffects": false, "main": "dist/index.cjs", "module": "dist/index.module.js", "source": "index.tsx", "unpkg": "./dist/index.umd.js", "types": "dist/index.d.ts", "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-typescript": "^7.23.6", "@changesets/cli": "^2.27.1", "@matejmazur/react-katex": "^3.1.3", "@size-limit/preset-small-lib": "^11.0.1", "@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@types/react": "^17.0.74", "benchtable": "^0.1.0", "cli-progress": "^3.12.0", "in-publish": "^2.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-serializer-html": "^7.1.0", "jest-watch-typeahead": "^2.2.2", "markdown-it": "^14.0.0", "markdown-to-jsx-latest": "npm:markdown-to-jsx@7.7.10", "microbundle": "^0.15.1", "microtime": "^3.1.1", "mkdirp": "^3.0.1", "preact": "^10.19.3", "prettier": "^2.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "rimraf": "^5.0.5", "simple-markdown": "^0.7.3", "size-limit": "^11.0.1", "styled-components": "^6.1.6", "theredoc": "^1.0.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "peerDependencies": {"react": ">= 0.14.0"}, "scripts": {"prepublish": "in-publish && npm run build && npm run release || not-in-publish", "prebuild": "rimraf dist && mkdirp dist", "build": "microbundle --tsconfig tsconfig.json -f cjs,umd index.cjs.tsx --name MarkdownToJSX --define process.env.NODE_ENV=production --globals react=React && microbundle --tsconfig tsconfig.json -f es,modern --name MarkdownToJSX --define process.env.NODE_ENV=production", "build-site": "microbundle site.tsx -o docs -f iife --tsconfig tsconfig.site.json --define process.env.NODE_ENV=production --jsx React.createElement --external react,react-dom,styled-components,katex --globals react=React,react-dom=ReactDOM,styled-components=styled --no-pkg-main", "dev": "microbundle watch site.tsx -o docs -f iife --tsconfig tsconfig.site.json --define process.env.NODE_ENV=development --jsx React.createElement --external react,react-dom,styled-components,katex --globals react=React,react-dom=ReactDOM,styled-components=styled --no-pkg-main", "test": "jest --verbose", "size": "size-limit", "benchmark": "node benchmark.js", "benchmark:all": "node benchmark.js --all", "changeset-publish": "yarn build && changeset publish"}, "size-limit": [{"path": "./dist/index.module.js", "limit": "6.75 kB"}, {"path": "./dist/index.modern.js", "limit": "6.75 kB"}], "jest": {"clearMocks": true, "testEnvironment": "jsdom", "transform": {"^.+\\.[tj]sx?$": ["ts-jest", {"diagnostics": {"ignoreCodes": ["TS151001"]}}]}, "snapshotSerializers": ["jest-serializer-html"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "packageManager": "yarn@4.6.0"}