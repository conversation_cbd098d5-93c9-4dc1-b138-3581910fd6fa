{"version": 3, "file": "index.modern.js", "sources": ["../index.tsx"], "sourcesContent": ["/* @jsx h */\n/**\n * markdown-to-jsx is a fork of\n * [simple-markdown v0.2.2](https://github.com/Khan/simple-markdown)\n * from Khan Academy. Thank you <PERSON> devs for making such an awesome\n * and extensible parsing infra... without it, half of the\n * optimizations here wouldn't be feasible. 🙏🏼\n */\nimport * as React from 'react'\n\n/**\n * Analogous to `node.type`. Please note that the values here may change at any time,\n * so do not hard code against the value directly.\n */\nexport const RuleType = {\n  blockQuote: '0',\n  breakLine: '1',\n  breakThematic: '2',\n  codeBlock: '3',\n  codeFenced: '4',\n  codeInline: '5',\n  footnote: '6',\n  footnoteReference: '7',\n  gfmTask: '8',\n  heading: '9',\n  headingSetext: '10',\n  /** only available if not `disableHTMLParsing` */\n  htmlBlock: '11',\n  htmlComment: '12',\n  /** only available if not `disableHTMLParsing` */\n  htmlSelfClosing: '13',\n  image: '14',\n  link: '15',\n  /** emits a `link` 'node', does not render directly */\n  linkAngleBraceStyleDetector: '16',\n  /** emits a `link` 'node', does not render directly */\n  linkBareUrlDetector: '17',\n  /** emits a `link` 'node', does not render directly */\n  linkMailtoDetector: '18',\n  newlineCoalescer: '19',\n  orderedList: '20',\n  paragraph: '21',\n  ref: '22',\n  refImage: '23',\n  refLink: '24',\n  table: '25',\n  tableSeparator: '26',\n  text: '27',\n  textBolded: '28',\n  textEmphasized: '29',\n  textEscaped: '30',\n  textMarked: '31',\n  textStrikethroughed: '32',\n  unorderedList: '33',\n} as const\n\nif (process.env.NODE_ENV === 'test') {\n  Object.keys(RuleType).forEach(key => (RuleType[key] = key))\n}\n\nexport type RuleType = (typeof RuleType)[keyof typeof RuleType]\n\nconst Priority = {\n  /**\n   * anything that must scan the tree before everything else\n   */\n  MAX: 0,\n  /**\n   * scans for block-level constructs\n   */\n  HIGH: 1,\n  /**\n   * inline w/ more priority than other inline\n   */\n  MED: 2,\n  /**\n   * inline elements\n   */\n  LOW: 3,\n  /**\n   * bare text and stuff that is considered leftovers\n   */\n  MIN: 4,\n}\n\n/** TODO: Drop for React 16? */\nconst ATTRIBUTE_TO_JSX_PROP_MAP = [\n  'allowFullScreen',\n  'allowTransparency',\n  'autoComplete',\n  'autoFocus',\n  'autoPlay',\n  'cellPadding',\n  'cellSpacing',\n  'charSet',\n  'classId',\n  'colSpan',\n  'contentEditable',\n  'contextMenu',\n  'crossOrigin',\n  'encType',\n  'formAction',\n  'formEncType',\n  'formMethod',\n  'formNoValidate',\n  'formTarget',\n  'frameBorder',\n  'hrefLang',\n  'inputMode',\n  'keyParams',\n  'keyType',\n  'marginHeight',\n  'marginWidth',\n  'maxLength',\n  'mediaGroup',\n  'minLength',\n  'noValidate',\n  'radioGroup',\n  'readOnly',\n  'rowSpan',\n  'spellCheck',\n  'srcDoc',\n  'srcLang',\n  'srcSet',\n  'tabIndex',\n  'useMap',\n].reduce(\n  (obj, x) => {\n    obj[x.toLowerCase()] = x\n    return obj\n  },\n  { class: 'className', for: 'htmlFor' }\n)\n\nconst namedCodesToUnicode = {\n  amp: '\\u0026',\n  apos: '\\u0027',\n  gt: '\\u003e',\n  lt: '\\u003c',\n  nbsp: '\\u00a0',\n  quot: '\\u201c',\n} as const\n\nconst DO_NOT_PROCESS_HTML_ELEMENTS = ['style', 'script', 'pre']\nconst ATTRIBUTES_TO_SANITIZE = [\n  'src',\n  'href',\n  'data',\n  'formAction',\n  'srcDoc',\n  'action',\n]\n\n/**\n * the attribute extractor regex looks for a valid attribute name,\n * followed by an equal sign (whitespace around the equal sign is allowed), followed\n * by one of the following:\n *\n * 1. a single quote-bounded string, e.g. 'foo'\n * 2. a double quote-bounded string, e.g. \"bar\"\n * 3. an interpolation, e.g. {something}\n *\n * JSX can be be interpolated into itself and is passed through the compiler using\n * the same options and setup as the current run.\n *\n * <Something children={<SomeOtherThing />} />\n *                      ==================\n *                              ↳ children: [<SomeOtherThing />]\n *\n * Otherwise, interpolations are handled as strings or simple booleans\n * unless HTML syntax is detected.\n *\n * <Something color={green} disabled={true} />\n *                   =====            ====\n *                     ↓                ↳ disabled: true\n *                     ↳ color: \"green\"\n *\n * Numbers are not parsed at this time due to complexities around int, float,\n * and the upcoming bigint functionality that would make handling it unwieldy.\n * Parse the string in your component as desired.\n *\n * <Something someBigNumber={123456789123456789} />\n *                           ==================\n *                                   ↳ someBigNumber: \"123456789123456789\"\n */\nconst ATTR_EXTRACTOR_R =\n  /([-A-Z0-9_:]+)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|(?:\\{((?:\\\\.|{[^}]*?}|[^}])*)\\})))?/gi\n\n/** TODO: Write explainers for each of these */\n\nconst AUTOLINK_MAILTO_CHECK_R = /mailto:/i\nconst BLOCK_END_R = /\\n{2,}$/\nconst BLOCKQUOTE_R = /^(\\s*>[\\s\\S]*?)(?=\\n\\n|$)/\nconst BLOCKQUOTE_TRIM_LEFT_MULTILINE_R = /^ *> ?/gm\nconst BLOCKQUOTE_ALERT_R = /^(?:\\[!([^\\]]*)\\]\\n)?([\\s\\S]*)/\nconst BREAK_LINE_R = /^ {2,}\\n/\nconst BREAK_THEMATIC_R = /^(?:( *[-*_])){3,} *(?:\\n *)+\\n/\nconst CODE_BLOCK_FENCED_R =\n  /^(?: {1,3})?(`{3,}|~{3,}) *(\\S+)? *([^\\n]*?)?\\n([\\s\\S]*?)(?:\\1\\n?|$)/\nconst CODE_BLOCK_R = /^(?: {4}[^\\n]+\\n*)+(?:\\n *)+\\n?/\nconst CODE_INLINE_R = /^(`+)((?:\\\\`|(?!\\1)`|[^`])+)\\1/\nconst CONSECUTIVE_NEWLINE_R = /^(?:\\n *)*\\n/\nconst CR_NEWLINE_R = /\\r\\n?/g\n\n/**\n * Matches footnotes on the format:\n *\n * [^key]: value\n *\n * Matches multiline footnotes\n *\n * [^key]: row\n * row\n * row\n *\n * And empty lines in indented multiline footnotes\n *\n * [^key]: indented with\n *     row\n *\n *     row\n *\n * Explanation:\n *\n * 1. Look for the starting tag, eg: [^key]\n *    ^\\[\\^([^\\]]+)]\n *\n * 2. The first line starts with a colon, and continues for the rest of the line\n *   :(.*)\n *\n * 3. Parse as many additional lines as possible. Matches new non-empty lines that doesn't begin with a new footnote definition.\n *    (\\n(?!\\[\\^).+)\n *\n * 4. ...or allows for repeated newlines if the next line begins with at least four whitespaces.\n *    (\\n+ {4,}.*)\n */\nconst FOOTNOTE_R = /^\\[\\^([^\\]]+)](:(.*)((\\n+ {4,}.*)|(\\n(?!\\[\\^).+))*)/\n\nconst FOOTNOTE_REFERENCE_R = /^\\[\\^([^\\]]+)]/\nconst FORMFEED_R = /\\f/g\nconst FRONT_MATTER_R = /^---[ \\t]*\\n(.|\\n)*\\n---[ \\t]*\\n/\nconst GFM_TASK_R = /^\\s*?\\[(x|\\s)\\]/\nconst HEADING_R = /^ *(#{1,6}) *([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_ATX_COMPLIANT_R =\n  /^ *(#{1,6}) +([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_SETEXT_R = /^([^\\n]+)\\n *(=|-){3,} *\\n/\n\n/**\n * Explanation:\n *\n * 1. Look for a starting tag, preceded by any amount of spaces\n *    ^ *<\n *\n * 2. Capture the tag name (capture 1)\n *    ([^ >/]+)\n *\n * 3. Ignore a space after the starting tag and capture the attribute portion of the tag (capture 2)\n *     ?([^>]*)>\n *\n * 4. Ensure a matching closing tag is present in the rest of the input string\n *    (?=[\\s\\S]*<\\/\\1>)\n *\n * 5. Capture everything until the matching closing tag -- this might include additional pairs\n *    of the same tag type found in step 2 (capture 3)\n *    ((?:[\\s\\S]*?(?:<\\1[^>]*>[\\s\\S]*?<\\/\\1>)*[\\s\\S]*?)*?)<\\/\\1>\n *\n * 6. Capture excess newlines afterward\n *    \\n*\n */\nconst HTML_BLOCK_ELEMENT_R =\n  /^ *(?!<[a-z][^ >/]* ?\\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\\n?(\\s*(?:<\\1[^>]*?>[\\s\\S]*?<\\/\\1>|(?!<\\1\\b)[\\s\\S])*?)<\\/\\1>(?!<\\/\\1>)\\n*/i\n\nconst HTML_CHAR_CODE_R = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi\n\nconst HTML_COMMENT_R = /^<!--[\\s\\S]*?(?:-->)/\n\n/**\n * borrowed from React 15(https://github.com/facebook/react/blob/894d20744cba99383ffd847dbd5b6e0800355a5c/src/renderers/dom/shared/HTMLDOMPropertyConfig.js)\n */\nconst HTML_CUSTOM_ATTR_R = /^(data|aria|x)-[a-z_][a-z\\d_.-]*$/\n\nconst HTML_SELF_CLOSING_ELEMENT_R =\n  /^ *<([a-z][a-z0-9:]*)(?:\\s+((?:<.*?>|[^>])*))?\\/?>(?!<\\/\\1>)(\\s*\\n)?/i\nconst INTERPOLATION_R = /^\\{.*\\}$/\nconst LINK_AUTOLINK_BARE_URL_R = /^(https?:\\/\\/[^\\s<]+[^<.,:;\"')\\]\\s])/\nconst LINK_AUTOLINK_MAILTO_R = /^<([^ >]+@[^ >]+)>/\nconst LINK_AUTOLINK_R = /^<([^ >]+:\\/[^ >]+)>/\nconst CAPTURE_LETTER_AFTER_HYPHEN = /-([a-z])?/gi\nconst NP_TABLE_R = /^(\\|.*)\\n(?: *(\\|? *[-:]+ *\\|[-| :]*)\\n((?:.*\\|.*\\n)*))?\\n?/\nconst PARAGRAPH_R = /^[^\\n]+(?:  \\n|\\n{2,})/\nconst REFERENCE_IMAGE_OR_LINK = /^\\[([^\\]]*)\\]:\\s+<?([^\\s>]+)>?\\s*(\"([^\"]*)\")?/\nconst REFERENCE_IMAGE_R = /^!\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst REFERENCE_LINK_R = /^\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst SHOULD_RENDER_AS_BLOCK_R = /(\\n|^[-*]\\s|^#|^ {2,}|^-{2,}|^>\\s)/\nconst TAB_R = /\\t/g\nconst TABLE_TRIM_PIPES = /(^ *\\||\\| *$)/g\nconst TABLE_CENTER_ALIGN = /^ *:-+: *$/\nconst TABLE_LEFT_ALIGN = /^ *:-+ *$/\nconst TABLE_RIGHT_ALIGN = /^ *-+: *$/\n\n/**\n * For inline formatting, this partial attempts to ignore characters that\n * may appear in nested formatting that could prematurely trigger detection\n * and therefore miss content that should have been included.\n */\nconst INLINE_SKIP_R =\n  '((?:\\\\[.*?\\\\][([].*?[)\\\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\\\\\\\1|[\\\\s\\\\S])+?)'\n\n/**\n * Detect a sequence like **foo** or __foo__. Note that bold has a higher priority\n * than emphasized to support nesting of both since they share a delimiter.\n */\nconst TEXT_BOLD_R = new RegExp(`^([*_])\\\\1${INLINE_SKIP_R}\\\\1\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like *foo* or _foo_.\n */\nconst TEXT_EMPHASIZED_R = new RegExp(`^([*_])${INLINE_SKIP_R}\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like ==foo==.\n */\nconst TEXT_MARKED_R = new RegExp(`^(==)${INLINE_SKIP_R}\\\\1`)\n\n/**\n * Detect a sequence like ~~foo~~.\n */\nconst TEXT_STRIKETHROUGHED_R = new RegExp(`^(~~)${INLINE_SKIP_R}\\\\1`)\n\n/**\n * Special case for shortcodes like :big-smile: or :emoji:\n */\nconst SHORTCODE_R = /^(:[a-zA-Z0-9-_]+:)/\n\nconst TEXT_ESCAPED_R = /^\\\\([^0-9A-Za-z\\s])/\nconst UNESCAPE_R = /\\\\([^0-9A-Za-z\\s])/g\n\n/**\n * Always take the first character, then eagerly take text until a double space\n * (potential line break) or some markdown-like punctuation is reached.\n */\nconst TEXT_PLAIN_R = /^[\\s\\S](?:(?!  \\n|[0-9]\\.|http)[^=*_~\\-\\n:<`\\\\\\[!])*/\n\nconst TRIM_STARTING_NEWLINES = /^\\n+/\n\nconst HTML_LEFT_TRIM_AMOUNT_R = /^([ \\t]*)/\n\ntype LIST_TYPE = 1 | 2\nconst ORDERED: LIST_TYPE = 1\nconst UNORDERED: LIST_TYPE = 2\n\nconst LIST_LOOKBEHIND_R = /(?:^|\\n)( *)$/\n\n// recognize a `*` `-`, `+`, `1.`, `2.`... list bullet\nconst ORDERED_LIST_BULLET = '(?:\\\\d+\\\\.)'\nconst UNORDERED_LIST_BULLET = '(?:[*+-])'\n\nfunction generateListItemPrefix(type: LIST_TYPE) {\n  return (\n    '( *)(' +\n    (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n    ') +'\n  )\n}\n\n// recognize the start of a list item:\n// leading space plus a bullet plus a space (`   * `)\nconst ORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(UNORDERED)\n\nfunction generateListItemPrefixRegex(type: LIST_TYPE) {\n  return new RegExp(\n    '^' +\n      (type === ORDERED ? ORDERED_LIST_ITEM_PREFIX : UNORDERED_LIST_ITEM_PREFIX)\n  )\n}\n\nconst ORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(UNORDERED)\n\nfunction generateListItemRegex(type: LIST_TYPE) {\n  // recognize an individual list item:\n  //  * hi\n  //    this is part of the same item\n  //\n  //    as is this, which is a new paragraph in the same item\n  //\n  //  * but this is not part of the same item\n  return new RegExp(\n    '^' +\n      (type === ORDERED\n        ? ORDERED_LIST_ITEM_PREFIX\n        : UNORDERED_LIST_ITEM_PREFIX) +\n      '[^\\\\n]*(?:\\\\n' +\n      '(?!\\\\1' +\n      (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n      ' )[^\\\\n]*)*(\\\\n|$)',\n    'gm'\n  )\n}\n\nconst ORDERED_LIST_ITEM_R = generateListItemRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_R = generateListItemRegex(UNORDERED)\n\n// check whether a list item has paragraphs: if it does,\n// we leave the newlines at the end\nfunction generateListRegex(type: LIST_TYPE) {\n  const bullet = type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET\n\n  return new RegExp(\n    '^( *)(' +\n      bullet +\n      ') ' +\n      '[\\\\s\\\\S]+?(?:\\\\n{2,}(?! )' +\n      '(?!\\\\1' +\n      bullet +\n      ' (?!' +\n      bullet +\n      ' ))\\\\n*' +\n      // the \\\\s*$ here is so that we can parse the inside of nested\n      // lists, where our content might end before we receive two `\\n`s\n      '|\\\\s*\\\\n*$)'\n  )\n}\n\nconst ORDERED_LIST_R = generateListRegex(ORDERED)\nconst UNORDERED_LIST_R = generateListRegex(UNORDERED)\n\nfunction generateListRule(\n  h: any,\n  type: LIST_TYPE\n): MarkdownToJSX.Rule<\n  MarkdownToJSX.OrderedListNode | MarkdownToJSX.UnorderedListNode\n> {\n  const ordered = type === ORDERED\n  const LIST_R = ordered ? ORDERED_LIST_R : UNORDERED_LIST_R\n  const LIST_ITEM_R = ordered ? ORDERED_LIST_ITEM_R : UNORDERED_LIST_ITEM_R\n  const LIST_ITEM_PREFIX_R = ordered\n    ? ORDERED_LIST_ITEM_PREFIX_R\n    : UNORDERED_LIST_ITEM_PREFIX_R\n\n  return {\n    _qualify: source => LIST_ITEM_PREFIX_R.test(source),\n    _match: allowInline(function (source, state) {\n      // We only want to break into a list if we are at the start of a\n      // line. This is to avoid parsing \"hi * there\" with \"* there\"\n      // becoming a part of a list.\n      // You might wonder, \"but that's inline, so of course it wouldn't\n      // start a list?\". You would be correct! Except that some of our\n      // lists can be inline, because they might be inside another list,\n      // in which case we can parse with inline scope, but need to allow\n      // nested lists inside this inline scope.\n      const isStartOfLine = LIST_LOOKBEHIND_R.exec(state.prevCapture)\n      const isListAllowed = state.list || (!state.inline && !state.simple)\n\n      if (isStartOfLine && isListAllowed) {\n        source = isStartOfLine[1] + source\n\n        return LIST_R.exec(source)\n      } else {\n        return null\n      }\n    }),\n    _order: Priority.HIGH,\n    _parse(capture, parse, state) {\n      const bullet = capture[2]\n      const start = ordered ? +bullet : undefined\n      const items = capture[0]\n        // recognize the end of a paragraph block inside a list item:\n        // two or more newlines at end end of the item\n        .replace(BLOCK_END_R, '\\n')\n        .match(LIST_ITEM_R)\n\n      let lastItemWasAParagraph = false\n\n      const itemContent = items.map(function (item, i) {\n        // We need to see how far indented the item is:\n        const space = LIST_ITEM_PREFIX_R.exec(item)[0].length\n\n        // And then we construct a regex to \"unindent\" the subsequent\n        // lines of the items by that amount:\n        const spaceRegex = new RegExp('^ {1,' + space + '}', 'gm')\n\n        // Before processing the item, we need a couple things\n        const content = item\n          // remove indents on trailing lines:\n          .replace(spaceRegex, '')\n          // remove the bullet:\n          .replace(LIST_ITEM_PREFIX_R, '')\n\n        // Handling \"loose\" lists, like:\n        //\n        //  * this is wrapped in a paragraph\n        //\n        //  * as is this\n        //\n        //  * as is this\n        const isLastItem = i === items.length - 1\n        const containsBlocks = content.indexOf('\\n\\n') !== -1\n\n        // Any element in a list is a block if it contains multiple\n        // newlines. The last element in the list can also be a block\n        // if the previous item in the list was a block (this is\n        // because non-last items in the list can end with \\n\\n, but\n        // the last item can't, so we just \"inherit\" this property\n        // from our previous element).\n        const thisItemIsAParagraph =\n          containsBlocks || (isLastItem && lastItemWasAParagraph)\n        lastItemWasAParagraph = thisItemIsAParagraph\n\n        // backup our state for delta afterwards. We're going to\n        // want to set state.list to true, and state.inline depending\n        // on our list's looseness.\n        const oldStateInline = state.inline\n        const oldStateList = state.list\n        state.list = true\n\n        // Parse inline if we're in a tight list, or block if we're in\n        // a loose list.\n        let adjustedContent\n        if (thisItemIsAParagraph) {\n          state.inline = false\n          adjustedContent = trimEnd(content) + '\\n\\n'\n        } else {\n          state.inline = true\n          adjustedContent = trimEnd(content)\n        }\n\n        const result = parse(adjustedContent, state)\n\n        // Restore our state before returning\n        state.inline = oldStateInline\n        state.list = oldStateList\n\n        return result\n      })\n\n      return {\n        items: itemContent,\n        ordered: ordered,\n        start: start,\n      }\n    },\n    _render(node, output, state) {\n      const Tag = node.ordered ? 'ol' : 'ul'\n\n      return (\n        <Tag\n          key={state.key}\n          start={node.type === RuleType.orderedList ? node.start : undefined}\n        >\n          {node.items.map(function generateListItem(item, i) {\n            return <li key={i}>{output(item, state)}</li>\n          })}\n        </Tag>\n      )\n    },\n  }\n}\n\nconst LINK_INSIDE = '(?:\\\\[[^\\\\[\\\\]]*(?:\\\\[[^\\\\[\\\\]]*\\\\][^\\\\[\\\\]]*)*\\\\]|[^\\\\[\\\\]])*'\nconst LINK_HREF_AND_TITLE =\n  '\\\\s*<?((?:\\\\([^)]*\\\\)|[^\\\\s\\\\\\\\]|\\\\\\\\.)*?)>?(?:\\\\s+[\\'\"]([\\\\s\\\\S]*?)[\\'\"])?\\\\s*'\nconst LINK_R = new RegExp(\n  '^\\\\[(' + LINK_INSIDE + ')\\\\]\\\\(' + LINK_HREF_AND_TITLE + '\\\\)'\n)\nconst IMAGE_R = /^!\\[(.*?)\\]\\( *((?:\\([^)]*\\)|[^() ])*) *\"?([^)\"]*)?\"?\\)/\n\nfunction trimEnd(str: string) {\n  let end = str.length\n  while (end > 0 && str[end - 1] <= ' ') end--\n  return str.slice(0, end)\n}\n\nfunction startsWith(str: string, prefix: string) {\n  return str.startsWith(prefix)\n}\n\nfunction qualifies(\n  source: string,\n  state: MarkdownToJSX.State,\n  qualify: MarkdownToJSX.Rule<any>['_qualify']\n) {\n  if (Array.isArray(qualify)) {\n    for (let i = 0; i < qualify.length; i++) {\n      if (startsWith(source, qualify[i])) return true\n    }\n\n    return false\n  }\n\n  return qualify(source, state)\n}\n\n/** Remove symmetrical leading and trailing quotes */\nfunction unquote(str: string) {\n  const first = str[0]\n  if (\n    (first === '\"' || first === \"'\") &&\n    str.length >= 2 &&\n    str[str.length - 1] === first\n  ) {\n    return str.slice(1, -1)\n  }\n  return str\n}\n\n// based on https://stackoverflow.com/a/18123682/1141611\n// not complete, but probably good enough\nexport function slugify(str: string) {\n  return str\n    .replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, 'a')\n    .replace(/[çÇ]/g, 'c')\n    .replace(/[ðÐ]/g, 'd')\n    .replace(/[ÈÉÊËéèêë]/g, 'e')\n    .replace(/[ÏïÎîÍíÌì]/g, 'i')\n    .replace(/[Ññ]/g, 'n')\n    .replace(/[øØœŒÕõÔôÓóÒò]/g, 'o')\n    .replace(/[ÜüÛûÚúÙù]/g, 'u')\n    .replace(/[ŸÿÝý]/g, 'y')\n    .replace(/[^a-z0-9- ]/gi, '')\n    .replace(/ /gi, '-')\n    .toLowerCase()\n}\n\nfunction parseTableAlignCapture(alignCapture: string) {\n  if (TABLE_RIGHT_ALIGN.test(alignCapture)) {\n    return 'right'\n  } else if (TABLE_CENTER_ALIGN.test(alignCapture)) {\n    return 'center'\n  } else if (TABLE_LEFT_ALIGN.test(alignCapture)) {\n    return 'left'\n  }\n\n  return null\n}\n\nfunction parseTableRow(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State,\n  tableOutput: boolean\n): MarkdownToJSX.ParserResult[][] {\n  const prevInTable = state.inTable\n\n  state.inTable = true\n\n  let cells: MarkdownToJSX.ParserResult[][] = [[]]\n  let acc = ''\n\n  function flush() {\n    if (!acc) return\n\n    const cell = cells[cells.length - 1]\n    cell.push.apply(cell, parse(acc, state))\n    acc = ''\n  }\n\n  source\n    .trim()\n    // isolate situations where a pipe should be ignored (inline code, escaped, etc)\n    .split(/(`[^`]*`|\\\\\\||\\|)/)\n    .filter(Boolean)\n    .forEach((fragment, i, arr) => {\n      if (fragment.trim() === '|') {\n        flush()\n\n        if (tableOutput) {\n          if (i !== 0 && i !== arr.length - 1) {\n            // Split the current row\n            cells.push([])\n          }\n\n          return\n        }\n      }\n\n      acc += fragment\n    })\n\n  flush()\n\n  state.inTable = prevInTable\n\n  return cells\n}\n\nfunction parseTableAlign(source: string /*, parse, state*/) {\n  const alignText = source.replace(TABLE_TRIM_PIPES, '').split('|')\n\n  return alignText.map(parseTableAlignCapture)\n}\n\nfunction parseTableCells(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  const rowsText = source.trim().split('\\n')\n\n  return rowsText.map(function (rowText) {\n    return parseTableRow(rowText, parse, state, true)\n  })\n}\n\nfunction parseTable(\n  capture: RegExpMatchArray,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  /**\n   * The table syntax makes some other parsing angry so as a bit of a hack even if alignment and/or cell rows are missing,\n   * we'll still run a detected first row through the parser and then just emit a paragraph.\n   */\n  state.inline = true\n  const align = capture[2] ? parseTableAlign(capture[2]) : []\n  const cells = capture[3] ? parseTableCells(capture[3], parse, state) : []\n  const header = parseTableRow(capture[1], parse, state, !!cells.length)\n  state.inline = false\n\n  return cells.length\n    ? {\n        align: align,\n        cells: cells,\n        header: header,\n        type: RuleType.table,\n      }\n    : {\n        children: header,\n        type: RuleType.paragraph,\n      }\n}\n\nfunction getTableStyle(node, colIndex) {\n  return node.align[colIndex] == null\n    ? {}\n    : {\n        textAlign: node.align[colIndex],\n      }\n}\n\n/** TODO: remove for react 16 */\nfunction normalizeAttributeKey(key) {\n  const hyphenIndex = key.indexOf('-')\n\n  if (hyphenIndex !== -1 && key.match(HTML_CUSTOM_ATTR_R) === null) {\n    key = key.replace(CAPTURE_LETTER_AFTER_HYPHEN, function (_, letter) {\n      return letter.toUpperCase()\n    })\n  }\n\n  return key\n}\n\ntype StyleTuple = [key: string, value: string]\n\nfunction parseStyleAttribute(styleString: string): StyleTuple[] {\n  const styles: StyleTuple[] = []\n  let buffer = ''\n  let inUrl = false\n  let inQuotes = false\n  let quoteChar: '\"' | \"'\" | '' = ''\n\n  if (!styleString) return styles\n\n  for (let i = 0; i < styleString.length; i++) {\n    const char = styleString[i]\n\n    // Handle quotes\n    if ((char === '\"' || char === \"'\") && !inUrl) {\n      if (!inQuotes) {\n        inQuotes = true\n        quoteChar = char\n      } else if (char === quoteChar) {\n        inQuotes = false\n        quoteChar = ''\n      }\n    }\n\n    // Track url() values\n    if (char === '(' && buffer.endsWith('url')) {\n      inUrl = true\n    } else if (char === ')' && inUrl) {\n      inUrl = false\n    }\n\n    // Only split on semicolons when not in quotes or url()\n    if (char === ';' && !inQuotes && !inUrl) {\n      const declaration = buffer.trim()\n      if (declaration) {\n        const colonIndex = declaration.indexOf(':')\n        if (colonIndex > 0) {\n          const key = declaration.slice(0, colonIndex).trim()\n          const value = declaration.slice(colonIndex + 1).trim()\n          styles.push([key, value])\n        }\n      }\n      buffer = ''\n    } else {\n      buffer += char\n    }\n  }\n\n  // Handle the last declaration\n  const declaration = buffer.trim()\n  if (declaration) {\n    const colonIndex = declaration.indexOf(':')\n    if (colonIndex > 0) {\n      const key = declaration.slice(0, colonIndex).trim()\n      const value = declaration.slice(colonIndex + 1).trim()\n      styles.push([key, value])\n    }\n  }\n\n  return styles\n}\n\nfunction attributeValueToJSXPropValue(\n  tag: MarkdownToJSX.HTMLTags,\n  key: keyof React.AllHTMLAttributes<Element>,\n  value: string,\n  sanitizeUrlFn: MarkdownToJSX.Options['sanitizer']\n): any {\n  if (key === 'style') {\n    return parseStyleAttribute(value).reduce(function (styles, [key, value]) {\n      // snake-case to camelCase\n      // also handles PascalCasing vendor prefixes\n      const camelCasedKey = key.replace(/(-[a-z])/g, substr =>\n        substr[1].toUpperCase()\n      )\n\n      // key.length + 1 to skip over the colon\n      styles[camelCasedKey] = sanitizeUrlFn(value, tag, key)\n\n      return styles\n    }, {})\n  } else if (ATTRIBUTES_TO_SANITIZE.indexOf(key) !== -1) {\n    return sanitizeUrlFn(unescape(value), tag, key)\n  } else if (value.match(INTERPOLATION_R)) {\n    // return as a string and let the consumer decide what to do with it\n    value = unescape(value.slice(1, value.length - 1))\n  }\n\n  if (value === 'true') {\n    return true\n  } else if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n\nfunction normalizeWhitespace(source: string): string {\n  return source\n    .replace(CR_NEWLINE_R, '\\n')\n    .replace(FORMFEED_R, '')\n    .replace(TAB_R, '    ')\n}\n\n/**\n * Creates a parser for a given set of rules, with the precedence\n * specified as a list of rules.\n *\n * @rules: an object containing\n * rule type -> {match, order, parse} objects\n * (lower order is higher precedence)\n * (Note: `order` is added to defaultRules after creation so that\n *  the `order` of defaultRules in the source matches the `order`\n *  of defaultRules in terms of `order` fields.)\n *\n * @returns The resulting parse function, with the following parameters:\n *   @source: the input source string to be parsed\n *   @state: an optional object to be threaded through parse\n *     calls. Allows clients to add stateful operations to\n *     parsing, such as keeping track of how many levels deep\n *     some nesting is. For an example use-case, see passage-ref\n *     parsing in src/widgets/passage/passage-markdown.jsx\n */\nfunction parserFor(\n  rules: MarkdownToJSX.Rules\n): (\n  source: string,\n  state: MarkdownToJSX.State\n) => ReturnType<MarkdownToJSX.NestedParser> {\n  var ruleList = Object.keys(rules)\n\n  if (process.env.NODE_ENV !== 'production') {\n    ruleList.forEach(function (type) {\n      const order = rules[type]._order\n      if (typeof order !== 'number' || !isFinite(order)) {\n        console.warn(\n          'markdown-to-jsx: Invalid order for rule `' + type + '`: ' + order\n        )\n      }\n    })\n  }\n\n  // Sorts rules in order of increasing order, then\n  // ascending rule name in case of ties.\n  ruleList.sort(function (a, b) {\n    return rules[a]._order - rules[b]._order || (a < b ? -1 : 1)\n  })\n\n  function nestedParse(\n    source: string,\n    state: MarkdownToJSX.State\n  ): MarkdownToJSX.ParserResult[] {\n    var result = []\n    state.prevCapture = state.prevCapture || ''\n\n    if (source.trim()) {\n      while (source) {\n        var i = 0\n        while (i < ruleList.length) {\n          var ruleType = ruleList[i]\n          var rule = rules[ruleType]\n\n          if (rule._qualify && !qualifies(source, state, rule._qualify)) {\n            i++\n            continue\n          }\n\n          var capture = rule._match(source, state)\n          if (capture && capture[0]) {\n            source = source.substring(capture[0].length)\n\n            var parsed = rule._parse(capture, nestedParse, state)\n\n            state.prevCapture += capture[0]\n\n            if (!parsed.type) parsed.type = ruleType as unknown as RuleType\n            result.push(parsed)\n            break\n          }\n          i++\n        }\n      }\n    }\n\n    // reset on exit\n    state.prevCapture = ''\n\n    return result\n  }\n\n  return function (source, state) {\n    return nestedParse(normalizeWhitespace(source), state)\n  }\n}\n\n/**\n * Marks a matcher function as eligible for being run inside an inline context;\n * allows us to do a little less work in the nested parser.\n */\nfunction allowInline<T extends Function & { inline?: 0 | 1 }>(fn: T) {\n  fn.inline = 1\n\n  return fn\n}\n\n// Creates a match function for an inline scoped or simple element from a regex\nfunction inlineRegex(regex: RegExp) {\n  return allowInline(function match(source, state: MarkdownToJSX.State) {\n    if (state.inline) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// basically any inline element except links\nfunction simpleInlineRegex(regex: RegExp) {\n  return allowInline(function match(\n    source: string,\n    state: MarkdownToJSX.State\n  ) {\n    if (state.inline || state.simple) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// Creates a match function for a block scoped element from a regex\nfunction blockRegex(regex: RegExp) {\n  return function match(source: string, state: MarkdownToJSX.State) {\n    if (state.inline || state.simple) {\n      return null\n    } else {\n      return regex.exec(source)\n    }\n  }\n}\n\n// Creates a match function from a regex, ignoring block/inline scope\nfunction anyScopeRegex(regex: RegExp) {\n  return allowInline(function match(source: string /*, state*/) {\n    return regex.exec(source)\n  })\n}\n\nconst SANITIZE_R = /(javascript|vbscript|data(?!:image)):/i\n\nexport function sanitizer(input: string): string {\n  try {\n    const decoded = decodeURIComponent(input).replace(/[^A-Za-z0-9/:]/g, '')\n\n    if (SANITIZE_R.test(decoded)) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'Input contains an unsafe JavaScript/VBScript/data expression, it will not be rendered.',\n          decoded\n        )\n      }\n\n      return null\n    }\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        'Input could not be decoded due to malformed syntax or characters, it will not be rendered.',\n        input\n      )\n    }\n\n    // decodeURIComponent sometimes throws a URIError\n    // See `decodeURIComponent('a%AFc');`\n    // http://stackoverflow.com/questions/9064536/javascript-decodeuricomponent-malformed-uri-exception\n    return null\n  }\n\n  return input\n}\n\nfunction unescape(rawString: string): string {\n  return rawString ? rawString.replace(UNESCAPE_R, '$1') : rawString\n}\n\n/**\n * Everything inline, including links.\n */\nfunction parseInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = true\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\n/**\n * Anything inline that isn't a link.\n */\nfunction parseSimpleInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = false\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\nfunction parseBlock(\n  parse,\n  children,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  state.inline = false\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  return result\n}\n\nconst parseCaptureInline: MarkdownToJSX.Parser<{\n  children: MarkdownToJSX.ParserResult[]\n}> = (capture, parse, state: MarkdownToJSX.State) => {\n  return {\n    children: parseInline(parse, capture[2], state),\n  }\n}\n\nfunction captureNothing() {\n  return {}\n}\n\nfunction renderNothing() {\n  return null\n}\n\nfunction reactFor(render) {\n  return function patchedRender(\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State = {}\n  ): React.ReactNode[] {\n    if (Array.isArray(ast)) {\n      const oldKey = state.key\n      const result = []\n\n      // map nestedOutput over the ast, except group any text\n      // nodes together into a single string output.\n      let lastWasString = false\n\n      for (let i = 0; i < ast.length; i++) {\n        state.key = i\n\n        const nodeOut = patchedRender(ast[i], state)\n        const isString = typeof nodeOut === 'string'\n\n        if (isString && lastWasString) {\n          result[result.length - 1] += nodeOut\n        } else if (nodeOut !== null) {\n          result.push(nodeOut)\n        }\n\n        lastWasString = isString\n      }\n\n      state.key = oldKey\n\n      return result\n    }\n\n    return render(ast, patchedRender, state)\n  }\n}\n\nfunction createRenderer(\n  rules: MarkdownToJSX.Rules,\n  userRender?: MarkdownToJSX.Options['renderRule']\n) {\n  return function renderRule(\n    ast: MarkdownToJSX.ParserResult,\n    render: MarkdownToJSX.RuleOutput,\n    state: MarkdownToJSX.State\n  ): React.ReactNode {\n    const renderer = rules[ast.type]._render as MarkdownToJSX.Rule['_render']\n\n    return userRender\n      ? userRender(() => renderer(ast, render, state), ast, render, state)\n      : renderer(ast, render, state)\n  }\n}\n\nfunction cx(...args) {\n  return args.filter(Boolean).join(' ')\n}\n\nfunction get(src: Object, path: string, fb?: any) {\n  let ptr = src\n  const frags = path.split('.')\n\n  while (frags.length) {\n    ptr = ptr[frags[0]]\n\n    if (ptr === undefined) break\n    else frags.shift()\n  }\n\n  return ptr || fb\n}\n\nfunction getTag(tag: string, overrides: MarkdownToJSX.Overrides) {\n  const override = get(overrides, tag)\n\n  if (!override) return tag\n\n  return typeof override === 'function' ||\n    (typeof override === 'object' && 'render' in override)\n    ? override\n    : get(overrides, `${tag}.component`, tag)\n}\n\nexport function compiler(\n  markdown: string = '',\n  options: MarkdownToJSX.Options = {}\n): React.JSX.Element {\n  options.overrides = options.overrides || {}\n  options.namedCodesToUnicode = options.namedCodesToUnicode\n    ? { ...namedCodesToUnicode, ...options.namedCodesToUnicode }\n    : namedCodesToUnicode\n\n  const slug = options.slugify || slugify\n  const sanitize = options.sanitizer || sanitizer\n  const createElement = options.createElement || React.createElement\n\n  const NON_PARAGRAPH_BLOCK_SYNTAXES = [\n    BLOCKQUOTE_R,\n    CODE_BLOCK_FENCED_R,\n    CODE_BLOCK_R,\n    options.enforceAtxHeadings ? HEADING_ATX_COMPLIANT_R : HEADING_R,\n    HEADING_SETEXT_R,\n    NP_TABLE_R,\n    ORDERED_LIST_R,\n    UNORDERED_LIST_R,\n  ]\n\n  const BLOCK_SYNTAXES = [\n    ...NON_PARAGRAPH_BLOCK_SYNTAXES,\n    PARAGRAPH_R,\n    HTML_BLOCK_ELEMENT_R,\n    HTML_COMMENT_R,\n    HTML_SELF_CLOSING_ELEMENT_R,\n  ]\n\n  function containsBlockSyntax(input: string) {\n    return BLOCK_SYNTAXES.some(r => r.test(input))\n  }\n\n  function matchParagraph(source: string, state: MarkdownToJSX.State) {\n    if (\n      state.inline ||\n      state.simple ||\n      (state.inHTML &&\n        source.indexOf('\\n\\n') === -1 &&\n        state.prevCapture.indexOf('\\n\\n') === -1)\n    ) {\n      return null\n    }\n\n    let match = ''\n\n    source.split('\\n').every(line => {\n      line += '\\n'\n\n      // bail out on first sign of non-paragraph block\n      if (NON_PARAGRAPH_BLOCK_SYNTAXES.some(regex => regex.test(line))) {\n        return false\n      }\n\n      match += line\n\n      return !!line.trim()\n    })\n\n    const captured = trimEnd(match)\n    if (captured === '') {\n      return null\n    }\n\n    // parseCaptureInline expects the inner content to be at index 2\n    // because index 1 is the delimiter for text formatting syntaxes\n    return [match, , captured] as RegExpMatchArray\n  }\n\n  // JSX custom pragma\n  // eslint-disable-next-line no-unused-vars\n  function h(\n    // locally we always will render a known string tag\n    tag: MarkdownToJSX.HTMLTags,\n    props: Parameters<MarkdownToJSX.CreateElement>[1] & {\n      className?: string\n      id?: string\n    },\n    ...children\n  ) {\n    const overrideProps = get(options.overrides, `${tag}.props`, {})\n\n    return createElement(\n      getTag(tag, options.overrides),\n      {\n        ...props,\n        ...overrideProps,\n        className: cx(props?.className, overrideProps.className) || undefined,\n      },\n      ...children\n    )\n  }\n\n  function compile(input: string): React.JSX.Element {\n    input = input.replace(FRONT_MATTER_R, '')\n\n    let inline = false\n\n    if (options.forceInline) {\n      inline = true\n    } else if (!options.forceBlock) {\n      /**\n       * should not contain any block-level markdown like newlines, lists, headings,\n       * thematic breaks, blockquotes, tables, etc\n       */\n      inline = SHOULD_RENDER_AS_BLOCK_R.test(input) === false\n    }\n\n    const arr = emitter(\n      parser(\n        inline\n          ? input\n          : `${trimEnd(input).replace(TRIM_STARTING_NEWLINES, '')}\\n\\n`,\n        {\n          inline,\n        }\n      )\n    )\n\n    while (\n      typeof arr[arr.length - 1] === 'string' &&\n      !arr[arr.length - 1].trim()\n    ) {\n      arr.pop()\n    }\n\n    if (options.wrapper === null) {\n      return arr\n    }\n\n    const wrapper = options.wrapper || (inline ? 'span' : 'div')\n    let jsx\n\n    if (arr.length > 1 || options.forceWrapper) {\n      jsx = arr\n    } else if (arr.length === 1) {\n      jsx = arr[0]\n\n      // TODO: remove this for React 16\n      if (typeof jsx === 'string') {\n        return <span key=\"outer\">{jsx}</span>\n      } else {\n        return jsx\n      }\n    } else {\n      // TODO: return null for React 16\n      jsx = null\n    }\n\n    return createElement(wrapper, { key: 'outer' }, jsx) as React.JSX.Element\n  }\n\n  function attrStringToMap(\n    tag: MarkdownToJSX.HTMLTags,\n    str: string\n  ): React.JSX.IntrinsicAttributes {\n    if (!str || !str.trim()) {\n      return null\n    }\n\n    const attributes = str.match(ATTR_EXTRACTOR_R)\n    if (!attributes) {\n      return null\n    }\n\n    return attributes.reduce(function (map, raw) {\n      const delimiterIdx = raw.indexOf('=')\n\n      if (delimiterIdx !== -1) {\n        const key = normalizeAttributeKey(raw.slice(0, delimiterIdx)).trim()\n        const value = unquote(raw.slice(delimiterIdx + 1).trim())\n\n        const mappedKey = ATTRIBUTE_TO_JSX_PROP_MAP[key] || key\n\n        // bail out, not supported\n        if (mappedKey === 'ref') return map\n\n        const normalizedValue = (map[mappedKey] = attributeValueToJSXPropValue(\n          tag,\n          key,\n          value,\n          sanitize\n        ))\n\n        if (\n          typeof normalizedValue === 'string' &&\n          (HTML_BLOCK_ELEMENT_R.test(normalizedValue) ||\n            HTML_SELF_CLOSING_ELEMENT_R.test(normalizedValue))\n        ) {\n          map[mappedKey] = compile(normalizedValue.trim())\n        }\n      } else if (raw !== 'style') {\n        map[ATTRIBUTE_TO_JSX_PROP_MAP[raw] || raw] = true\n      }\n\n      return map\n    }, {})\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof markdown !== 'string') {\n      throw new Error(`markdown-to-jsx: the first argument must be\n                             a string`)\n    }\n\n    if (\n      Object.prototype.toString.call(options.overrides) !== '[object Object]'\n    ) {\n      throw new Error(`markdown-to-jsx: options.overrides (second argument property) must be\n                             undefined or an object literal with shape:\n                             {\n                                htmltagname: {\n                                    component: string|ReactComponent(optional),\n                                    props: object(optional)\n                                }\n                             }`)\n    }\n  }\n\n  const footnotes: { footnote: string; identifier: string }[] = []\n  const refs: { [key: string]: { target: string; title: string } } = {}\n\n  /**\n   * each rule's react() output function goes through our custom\n   * h() JSX pragma; this allows the override functionality to be\n   * automatically applied\n   */\n  // @ts-ignore\n  const rules: MarkdownToJSX.Rules = {\n    [RuleType.blockQuote]: {\n      _qualify: ['>'],\n      _match: blockRegex(BLOCKQUOTE_R),\n      _order: Priority.HIGH,\n      _parse(capture, parse, state) {\n        const [, alert, content] = capture[0]\n          .replace(BLOCKQUOTE_TRIM_LEFT_MULTILINE_R, '')\n          .match(BLOCKQUOTE_ALERT_R)\n\n        return {\n          alert,\n          children: parse(content, state),\n        }\n      },\n      _render(node, output, state) {\n        const props = {\n          key: state.key,\n        } as Record<string, unknown>\n\n        if (node.alert) {\n          props.className =\n            'markdown-alert-' + slug(node.alert.toLowerCase(), slugify)\n\n          node.children.unshift({\n            attrs: {},\n            children: [{ type: RuleType.text, text: node.alert }],\n            noInnerParse: true,\n            type: RuleType.htmlBlock,\n            tag: 'header',\n          })\n        }\n\n        return h('blockquote', props, output(node.children, state))\n      },\n    },\n\n    [RuleType.breakLine]: {\n      _match: anyScopeRegex(BREAK_LINE_R),\n      _order: Priority.HIGH,\n      _parse: captureNothing,\n      _render(_, __, state) {\n        return <br key={state.key} />\n      },\n    },\n\n    [RuleType.breakThematic]: {\n      _qualify: source => {\n        const char = source[0]\n        return char === '-' || char === '*' || char === '_'\n      },\n      _match: blockRegex(BREAK_THEMATIC_R),\n      _order: Priority.HIGH,\n      _parse: captureNothing,\n      _render(_, __, state) {\n        return <hr key={state.key} />\n      },\n    },\n\n    [RuleType.codeBlock]: {\n      _qualify: ['    '],\n      _match: blockRegex(CODE_BLOCK_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        return {\n          lang: undefined,\n          text: unescape(trimEnd(capture[0].replace(/^ {4}/gm, ''))),\n        }\n      },\n\n      _render(node, output, state) {\n        return (\n          <pre key={state.key}>\n            <code\n              {...node.attrs}\n              className={node.lang ? `lang-${node.lang}` : ''}\n            >\n              {node.text}\n            </code>\n          </pre>\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      attrs?: ReturnType<typeof attrStringToMap>\n      lang?: string\n      text: string\n    }>,\n\n    [RuleType.codeFenced]: {\n      _qualify: ['```', '~~~'],\n      _match: blockRegex(CODE_BLOCK_FENCED_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        return {\n          // if capture[3] it's additional metadata\n          attrs: attrStringToMap('code', capture[3] || ''),\n          lang: capture[2] || undefined,\n          text: capture[4],\n          type: RuleType.codeBlock,\n        }\n      },\n    },\n\n    [RuleType.codeInline]: {\n      _qualify: ['`'],\n      _match: simpleInlineRegex(CODE_INLINE_R),\n      _order: Priority.LOW,\n      _parse(capture /*, parse, state*/) {\n        return {\n          text: unescape(capture[2]),\n        }\n      },\n      _render(node, output, state) {\n        return <code key={state.key}>{node.text}</code>\n      },\n    },\n\n    /**\n     * footnotes are emitted at the end of compilation in a special <footer> block\n     */\n    [RuleType.footnote]: {\n      _qualify: ['[^'],\n      _match: blockRegex(FOOTNOTE_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        footnotes.push({\n          footnote: capture[2],\n          identifier: capture[1],\n        })\n\n        return {}\n      },\n      _render: renderNothing,\n    },\n\n    [RuleType.footnoteReference]: {\n      _qualify: ['[^'],\n      _match: inlineRegex(FOOTNOTE_REFERENCE_R),\n      _order: Priority.HIGH,\n      _parse(capture /*, parse*/) {\n        return {\n          target: `#${slug(capture[1], slugify)}`,\n          text: capture[1],\n        }\n      },\n      _render(node, output, state) {\n        return (\n          <a key={state.key} href={sanitize(node.target, 'a', 'href')}>\n            <sup key={state.key}>{node.text}</sup>\n          </a>\n        )\n      },\n    } as MarkdownToJSX.Rule<{ target: string; text: string }>,\n\n    [RuleType.gfmTask]: {\n      _qualify: ['[ ]', '[x]'],\n      _match: inlineRegex(GFM_TASK_R),\n      _order: Priority.HIGH,\n      _parse(capture /*, parse, state*/) {\n        return {\n          completed: capture[1].toLowerCase() === 'x',\n        }\n      },\n      _render(node, output, state) {\n        return (\n          <input\n            checked={node.completed}\n            key={state.key}\n            readOnly\n            type=\"checkbox\"\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{ completed: boolean }>,\n\n    [RuleType.heading]: {\n      _qualify: ['#'],\n      _match: blockRegex(\n        options.enforceAtxHeadings ? HEADING_ATX_COMPLIANT_R : HEADING_R\n      ),\n      _order: Priority.HIGH,\n      _parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[2], state),\n          id: slug(capture[2], slugify),\n          level: capture[1].length as MarkdownToJSX.HeadingNode['level'],\n        }\n      },\n      _render(node, output, state) {\n        return h(\n          `h${node.level}`,\n          { id: node.id, key: state.key },\n          output(node.children, state)\n        )\n      },\n    },\n\n    [RuleType.headingSetext]: {\n      _match: blockRegex(HEADING_SETEXT_R),\n      _order: Priority.MAX,\n      _parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[1], state),\n          level: capture[2] === '=' ? 1 : 2,\n          type: RuleType.heading,\n        }\n      },\n    },\n\n    [RuleType.htmlBlock]: {\n      _qualify: ['<'],\n      /**\n       * find the first matching end tag and process the interior\n       */\n      _match: anyScopeRegex(HTML_BLOCK_ELEMENT_R),\n      _order: Priority.HIGH,\n      _parse(capture, parse, state) {\n        const [, whitespace] = capture[3].match(HTML_LEFT_TRIM_AMOUNT_R)\n\n        const trimmer = new RegExp(`^${whitespace}`, 'gm')\n        const trimmed = capture[3].replace(trimmer, '')\n\n        const parseFunc = containsBlockSyntax(trimmed)\n          ? parseBlock\n          : parseInline\n\n        const tagName = capture[1].toLowerCase() as MarkdownToJSX.HTMLTags\n        const noInnerParse =\n          DO_NOT_PROCESS_HTML_ELEMENTS.indexOf(tagName) !== -1\n\n        const tag = (\n          noInnerParse ? tagName : capture[1]\n        ).trim() as MarkdownToJSX.HTMLTags\n\n        const ast = {\n          attrs: attrStringToMap(tag, capture[2]),\n          noInnerParse: noInnerParse,\n          tag,\n        } as {\n          attrs: ReturnType<typeof attrStringToMap>\n          children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n          noInnerParse: Boolean\n          tag: MarkdownToJSX.HTMLTags\n          text?: string | undefined\n        }\n\n        state.inAnchor = state.inAnchor || tagName === 'a'\n\n        if (noInnerParse) {\n          ast.text = capture[3]\n        } else {\n          const prevInHTML = state.inHTML\n          state.inHTML = true\n          ast.children = parseFunc(parse, trimmed, state)\n          state.inHTML = prevInHTML\n        }\n\n        /**\n         * if another html block is detected within, parse as block,\n         * otherwise parse as inline to pick up any further markdown\n         */\n        state.inAnchor = false\n\n        return ast\n      },\n      _render(node, output, state) {\n        return (\n          <node.tag key={state.key} {...node.attrs}>\n            {node.text || (node.children ? output(node.children, state) : '')}\n          </node.tag>\n        )\n      },\n    },\n\n    [RuleType.htmlSelfClosing]: {\n      _qualify: ['<'],\n      /**\n       * find the first matching end tag and process the interior\n       */\n      _match: anyScopeRegex(HTML_SELF_CLOSING_ELEMENT_R),\n      _order: Priority.HIGH,\n      _parse(capture /*, parse, state*/) {\n        const tag = capture[1].trim() as MarkdownToJSX.HTMLTags\n        return {\n          attrs: attrStringToMap(tag, capture[2] || ''),\n          tag,\n        }\n      },\n      _render(node, output, state) {\n        return <node.tag {...node.attrs} key={state.key} />\n      },\n    },\n\n    [RuleType.htmlComment]: {\n      _qualify: ['<!--'],\n      _match: anyScopeRegex(HTML_COMMENT_R),\n      _order: Priority.HIGH,\n      _parse() {\n        return {}\n      },\n      _render: renderNothing,\n    },\n\n    [RuleType.image]: {\n      _qualify: ['!['],\n      _match: simpleInlineRegex(IMAGE_R),\n      _order: Priority.HIGH,\n      _parse(capture /*, parse, state*/) {\n        return {\n          alt: unescape(capture[1]),\n          target: unescape(capture[2]),\n          title: unescape(capture[3]),\n        }\n      },\n      _render(node, output, state) {\n        return (\n          <img\n            key={state.key}\n            alt={node.alt || undefined}\n            title={node.title || undefined}\n            src={sanitize(node.target, 'img', 'src')}\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      alt?: string\n      target: string\n      title?: string\n    }>,\n\n    [RuleType.link]: {\n      _qualify: ['['],\n      _match: inlineRegex(LINK_R),\n      _order: Priority.LOW,\n      _parse(capture, parse, state) {\n        return {\n          children: parseSimpleInline(parse, capture[1], state),\n          target: unescape(capture[2]),\n          title: unescape(capture[3]),\n        }\n      },\n      _render(node, output, state) {\n        return (\n          <a\n            key={state.key}\n            href={sanitize(node.target, 'a', 'href')}\n            title={node.title}\n          >\n            {output(node.children, state)}\n          </a>\n        )\n      },\n    },\n\n    // https://daringfireball.net/projects/markdown/syntax#autolink\n    [RuleType.linkAngleBraceStyleDetector]: {\n      _qualify: ['<'],\n      _match: inlineRegex(LINK_AUTOLINK_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkBareUrlDetector]: {\n      _qualify: (source, state) => {\n        if (state.inAnchor || options.disableAutoLink) return false\n        return startsWith(source, 'http://') || startsWith(source, 'https://')\n      },\n      _match: inlineRegex(LINK_AUTOLINK_BARE_URL_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          title: undefined,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkMailtoDetector]: {\n      _qualify: ['<'],\n      _match: inlineRegex(LINK_AUTOLINK_MAILTO_R),\n      _order: Priority.MAX,\n      _parse(capture /*, parse, state*/) {\n        let address = capture[1]\n        let target = capture[1]\n\n        // Check for a `mailto:` already existing in the link:\n        if (!AUTOLINK_MAILTO_CHECK_R.test(target)) {\n          target = 'mailto:' + target\n        }\n\n        return {\n          children: [\n            {\n              text: address.replace('mailto:', ''),\n              type: RuleType.text,\n            },\n          ],\n          target: target,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.orderedList]: generateListRule(\n      h,\n      ORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.OrderedListNode>,\n\n    [RuleType.unorderedList]: generateListRule(\n      h,\n      UNORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.UnorderedListNode>,\n\n    [RuleType.newlineCoalescer]: {\n      _match: blockRegex(CONSECUTIVE_NEWLINE_R),\n      _order: Priority.LOW,\n      _parse: captureNothing,\n      _render(/*node, output, state*/) {\n        return '\\n'\n      },\n    },\n\n    [RuleType.paragraph]: {\n      _match: allowInline(matchParagraph),\n      _order: Priority.LOW,\n      _parse: parseCaptureInline,\n      _render(node, output, state) {\n        return <p key={state.key}>{output(node.children, state)}</p>\n      },\n    } as MarkdownToJSX.Rule<ReturnType<typeof parseCaptureInline>>,\n\n    [RuleType.ref]: {\n      _qualify: ['['],\n      _match: inlineRegex(REFERENCE_IMAGE_OR_LINK),\n      _order: Priority.MAX,\n      _parse(capture /*, parse*/) {\n        refs[capture[1]] = {\n          target: capture[2],\n          title: capture[4],\n        }\n\n        return {}\n      },\n      _render: renderNothing,\n    },\n\n    [RuleType.refImage]: {\n      _qualify: ['!['],\n      _match: simpleInlineRegex(REFERENCE_IMAGE_R),\n      _order: Priority.MAX,\n      _parse(capture) {\n        return {\n          alt: capture[1] ? unescape(capture[1]) : undefined,\n          ref: capture[2],\n        }\n      },\n      _render(node, output, state) {\n        return refs[node.ref] ? (\n          <img\n            key={state.key}\n            alt={node.alt}\n            src={sanitize(refs[node.ref].target, 'img', 'src')}\n            title={refs[node.ref].title}\n          />\n        ) : null\n      },\n    } as MarkdownToJSX.Rule<{ alt?: string; ref: string }>,\n\n    [RuleType.refLink]: {\n      _qualify: (source) => source[0] === '[' && source.indexOf('](') === -1,\n      _match: inlineRegex(REFERENCE_LINK_R),\n      _order: Priority.MAX,\n      _parse(capture, parse, state) {\n        return {\n          children: parse(capture[1], state),\n          fallbackChildren: capture[0],\n          ref: capture[2],\n        }\n      },\n      _render(node, output, state) {\n        return refs[node.ref] ? (\n          <a\n            key={state.key}\n            href={sanitize(refs[node.ref].target, 'a', 'href')}\n            title={refs[node.ref].title}\n          >\n            {output(node.children, state)}\n          </a>\n        ) : (\n          <span key={state.key}>{node.fallbackChildren}</span>\n        )\n      },\n    },\n\n    [RuleType.table]: {\n      _qualify: ['|'],\n      _match: blockRegex(NP_TABLE_R),\n      _order: Priority.HIGH,\n      _parse: parseTable,\n      _render(node, output, state) {\n        const table = node as MarkdownToJSX.TableNode\n        return (\n          <table key={state.key}>\n            <thead>\n              <tr>\n                {table.header.map(function generateHeaderCell(content, i) {\n                  return (\n                    <th key={i} style={getTableStyle(table, i)}>\n                      {output(content, state)}\n                    </th>\n                  )\n                })}\n              </tr>\n            </thead>\n\n            <tbody>\n              {table.cells.map(function generateTableRow(row, i) {\n                return (\n                  <tr key={i}>\n                    {row.map(function generateTableCell(content, c) {\n                      return (\n                        <td key={c} style={getTableStyle(table, c)}>\n                          {output(content, state)}\n                        </td>\n                      )\n                    })}\n                  </tr>\n                )\n              })}\n            </tbody>\n          </table>\n        )\n      },\n    },\n\n    [RuleType.text]: {\n      // Here we look for anything followed by non-symbols,\n      // double newlines, or double-space-newlines\n      // We break on any symbol characters so that this grammar\n      // is easy to extend without needing to modify this regex\n      _match: allowInline(function (source, state) {\n        let ret\n        if (startsWith(source, ':')) ret = SHORTCODE_R.exec(source)\n        if (ret) return ret\n\n        return TEXT_PLAIN_R.exec(source)\n      }),\n      _order: Priority.MIN,\n      _parse(capture) {\n        const text = capture[0]\n        return {\n          text:\n            text.indexOf('&') === -1\n              ? text\n              : text.replace(\n                  HTML_CHAR_CODE_R,\n                  (full, inner) => options.namedCodesToUnicode[inner] || full\n                ),\n        }\n      },\n      _render(node) {\n        return node.text\n      },\n    },\n\n    [RuleType.textBolded]: {\n      _qualify: ['**', '__'],\n      _match: simpleInlineRegex(TEXT_BOLD_R),\n      _order: Priority.MED,\n      _parse(capture, parse, state) {\n        return {\n          // capture[1] -> the syntax control character\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      _render(node, output, state) {\n        return <strong key={state.key}>{output(node.children, state)}</strong>\n      },\n    },\n\n    [RuleType.textEmphasized]: {\n      _qualify: source => {\n        const char = source[0]\n        return (char === '*' || char === '_') && source[1] !== char\n      },\n      _match: simpleInlineRegex(TEXT_EMPHASIZED_R),\n      _order: Priority.LOW,\n      _parse(capture, parse, state) {\n        return {\n          // capture[1] -> opening * or _\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      _render(node, output, state) {\n        return <em key={state.key}>{output(node.children, state)}</em>\n      },\n    },\n\n    [RuleType.textEscaped]: {\n      _qualify: ['\\\\'],\n      // We don't allow escaping numbers, letters, or spaces here so that\n      // backslashes used in plain text still get rendered. But allowing\n      // escaping anything else provides a very flexible escape mechanism,\n      // regardless of how this grammar is extended.\n      _match: simpleInlineRegex(TEXT_ESCAPED_R),\n      _order: Priority.HIGH,\n      _parse(capture /*, parse, state*/) {\n        return {\n          text: capture[1],\n          type: RuleType.text,\n        }\n      },\n    },\n\n    [RuleType.textMarked]: {\n      _qualify: ['=='],\n      _match: simpleInlineRegex(TEXT_MARKED_R),\n      _order: Priority.LOW,\n      _parse: parseCaptureInline,\n      _render(node, output, state) {\n        return <mark key={state.key}>{output(node.children, state)}</mark>\n      },\n    },\n\n    [RuleType.textStrikethroughed]: {\n      _qualify: ['~~'],\n      _match: simpleInlineRegex(TEXT_STRIKETHROUGHED_R),\n      _order: Priority.LOW,\n      _parse: parseCaptureInline,\n      _render(node, output, state) {\n        return <del key={state.key}>{output(node.children, state)}</del>\n      },\n    },\n  }\n\n  // Object.keys(rules).forEach(key => {\n  //   let { _match: match, parse: parse } = rules[key]\n\n  //   // rules[key].match = (...args) => {\n  //   //   const start = performance.now()\n  //   //   const result = match(...args)\n  //   //   const delta = performance.now() - start\n\n  //   //   if (delta > 5)\n  //   //     console.warn(\n  //   //       `Slow match for ${key}: ${delta.toFixed(3)}ms, input: ${args[0]}`\n  //   //     )\n\n  //   //   return result\n  //   // }\n\n  //   rules[key].parse = (...args) => {\n  //     const start = performance.now()\n  //     const result = parse(...args)\n  //     const delta = performance.now() - start\n\n  //     if (delta > 5) {\n  //       console.warn(\n  //         `Slow parse for ${key}: ${delta.toFixed(3)}ms, input: ${args[0]}`\n  //       )\n  //     }\n\n  //     // console[delta > 5 ? 'warn' : 'log'](\n  //     //   `${key}:parse`,\n  //     //   `${delta.toFixed(3)}ms`,\n  //     //   args[0]\n  //     // )\n\n  //     return result\n  //   }\n  // })\n\n  if (options.disableParsingRawHTML === true) {\n    delete rules[RuleType.htmlBlock]\n    delete rules[RuleType.htmlSelfClosing]\n  }\n\n  const parser = parserFor(rules)\n  const emitter: Function = reactFor(createRenderer(rules, options.renderRule))\n\n  const jsx = compile(markdown)\n\n  if (footnotes.length) {\n    return (\n      <div>\n        {jsx}\n        <footer key=\"footer\">\n          {footnotes.map(function createFootnote(def) {\n            return (\n              <div id={slug(def.identifier, slugify)} key={def.identifier}>\n                {def.identifier}\n                {emitter(parser(def.footnote, { inline: true }))}\n              </div>\n            )\n          })}\n        </footer>\n      </div>\n    )\n  }\n\n  return jsx\n}\n\n/**\n * A simple HOC for easy React use. Feed the markdown content as a direct child\n * and the rest is taken care of automatically.\n */\nconst Markdown: React.FC<\n  Omit<React.HTMLAttributes<Element>, 'children'> & {\n    children: string\n    options?: MarkdownToJSX.Options\n  }\n> = ({ children = '', options, ...props }) => {\n  if (process.env.NODE_ENV !== 'production' && typeof children !== 'string') {\n    console.error(\n      'markdown-to-jsx: <Markdown> component only accepts a single string as a child, received:',\n      children\n    )\n  }\n\n  return React.cloneElement(\n    compiler(children, options),\n    props as React.JSX.IntrinsicAttributes\n  )\n}\n\nexport namespace MarkdownToJSX {\n  /**\n   * RequireAtLeastOne<{ ... }> <- only requires at least one key\n   */\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>\n    }[Keys]\n\n  export type CreateElement = typeof React.createElement\n\n  export type HTMLTags = keyof React.JSX.IntrinsicElements\n\n  export type State = {\n    /** true if the current content is inside anchor link grammar */\n    inAnchor?: boolean\n    /** true if parsing in an HTML context */\n    inHTML?: boolean\n    /** true if parsing in an inline context (subset of rules around formatting and links) */\n    inline?: boolean\n    /** true if in a table */\n    inTable?: boolean\n    /** use this for the `key` prop */\n    key?: React.Key\n    /** true if in a list */\n    list?: boolean\n    /** used for lookbacks */\n    prevCapture?: string\n    /** true if parsing in inline context w/o links */\n    simple?: boolean\n  }\n\n  export interface BlockQuoteNode {\n    alert?: string\n    children: MarkdownToJSX.ParserResult[]\n    type: typeof RuleType.blockQuote\n  }\n\n  export interface BreakLineNode {\n    type: typeof RuleType.breakLine\n  }\n\n  export interface BreakThematicNode {\n    type: typeof RuleType.breakThematic\n  }\n\n  export interface CodeBlockNode {\n    type: typeof RuleType.codeBlock\n    attrs?: React.JSX.IntrinsicAttributes\n    lang?: string\n    text: string\n  }\n\n  export interface CodeFencedNode {\n    type: typeof RuleType.codeFenced\n  }\n\n  export interface CodeInlineNode {\n    type: typeof RuleType.codeInline\n    text: string\n  }\n\n  export interface FootnoteNode {\n    type: typeof RuleType.footnote\n  }\n\n  export interface FootnoteReferenceNode {\n    type: typeof RuleType.footnoteReference\n    target: string\n    text: string\n  }\n\n  export interface GFMTaskNode {\n    type: typeof RuleType.gfmTask\n    completed: boolean\n  }\n\n  export interface HeadingNode {\n    type: typeof RuleType.heading\n    children: MarkdownToJSX.ParserResult[]\n    id: string\n    level: 1 | 2 | 3 | 4 | 5 | 6\n  }\n\n  export interface HeadingSetextNode {\n    type: typeof RuleType.headingSetext\n  }\n\n  export interface HTMLCommentNode {\n    type: typeof RuleType.htmlComment\n  }\n\n  export interface ImageNode {\n    type: typeof RuleType.image\n    alt?: string\n    target: string\n    title?: string\n  }\n\n  export interface LinkNode {\n    type: typeof RuleType.link\n    children: MarkdownToJSX.ParserResult[]\n    target: string\n    title?: string\n  }\n\n  export interface LinkAngleBraceNode {\n    type: typeof RuleType.linkAngleBraceStyleDetector\n  }\n\n  export interface LinkBareURLNode {\n    type: typeof RuleType.linkBareUrlDetector\n  }\n\n  export interface LinkMailtoNode {\n    type: typeof RuleType.linkMailtoDetector\n  }\n\n  export interface OrderedListNode {\n    type: typeof RuleType.orderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: true\n    start?: number\n  }\n\n  export interface UnorderedListNode {\n    type: typeof RuleType.unorderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: false\n  }\n\n  export interface NewlineNode {\n    type: typeof RuleType.newlineCoalescer\n  }\n\n  export interface ParagraphNode {\n    type: typeof RuleType.paragraph\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ReferenceNode {\n    type: typeof RuleType.ref\n  }\n\n  export interface ReferenceImageNode {\n    type: typeof RuleType.refImage\n    alt?: string\n    ref: string\n  }\n\n  export interface ReferenceLinkNode {\n    type: typeof RuleType.refLink\n    children: MarkdownToJSX.ParserResult[]\n    fallbackChildren: string\n    ref: string\n  }\n\n  export interface TableNode {\n    type: typeof RuleType.table\n    /**\n     * alignment for each table column\n     */\n    align: ('left' | 'right' | 'center')[]\n    cells: MarkdownToJSX.ParserResult[][][]\n    header: MarkdownToJSX.ParserResult[][]\n  }\n\n  export interface TableSeparatorNode {\n    type: typeof RuleType.tableSeparator\n  }\n\n  export interface TextNode {\n    type: typeof RuleType.text\n    text: string\n  }\n\n  export interface BoldTextNode {\n    type: typeof RuleType.textBolded\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ItalicTextNode {\n    type: typeof RuleType.textEmphasized\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface EscapedTextNode {\n    type: typeof RuleType.textEscaped\n  }\n\n  export interface MarkedTextNode {\n    type: typeof RuleType.textMarked\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface StrikethroughTextNode {\n    type: typeof RuleType.textStrikethroughed\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface HTMLNode {\n    type: typeof RuleType.htmlBlock\n    attrs: React.JSX.IntrinsicAttributes\n    children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n    noInnerParse: Boolean\n    tag: MarkdownToJSX.HTMLTags\n    text?: string | undefined\n  }\n\n  export interface HTMLSelfClosingNode {\n    type: typeof RuleType.htmlSelfClosing\n    attrs: React.JSX.IntrinsicAttributes\n    tag: string\n  }\n\n  export type ParserResult =\n    | BlockQuoteNode\n    | BreakLineNode\n    | BreakThematicNode\n    | CodeBlockNode\n    | CodeFencedNode\n    | CodeInlineNode\n    | FootnoteNode\n    | FootnoteReferenceNode\n    | GFMTaskNode\n    | HeadingNode\n    | HeadingSetextNode\n    | HTMLCommentNode\n    | ImageNode\n    | LinkNode\n    | LinkAngleBraceNode\n    | LinkBareURLNode\n    | LinkMailtoNode\n    | OrderedListNode\n    | UnorderedListNode\n    | NewlineNode\n    | ParagraphNode\n    | ReferenceNode\n    | ReferenceImageNode\n    | ReferenceLinkNode\n    | TableNode\n    | TableSeparatorNode\n    | TextNode\n    | BoldTextNode\n    | ItalicTextNode\n    | EscapedTextNode\n    | MarkedTextNode\n    | StrikethroughTextNode\n    | HTMLNode\n    | HTMLSelfClosingNode\n\n  export type NestedParser = (\n    input: string,\n    state?: MarkdownToJSX.State\n  ) => MarkdownToJSX.ParserResult[]\n\n  export type Parser<ParserOutput> = (\n    capture: RegExpMatchArray,\n    nestedParse: NestedParser,\n    state?: MarkdownToJSX.State\n  ) => ParserOutput\n\n  export type RuleOutput = (\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State\n  ) => React.JSX.Element\n\n  export type Rule<ParserOutput = MarkdownToJSX.ParserResult> = {\n    _match: (\n      source: string,\n      state: MarkdownToJSX.State,\n      prevCapturedString?: string\n    ) => RegExpMatchArray\n    _order: (typeof Priority)[keyof typeof Priority]\n    _parse: MarkdownToJSX.Parser<Omit<ParserOutput, 'type'>>\n    /**\n     * Optional fast check that can quickly determine if this rule\n     * should even be attempted. Should check the start of the source string\n     * for quick patterns without expensive regex operations.\n     *\n     * @param source The input source string (already trimmed of leading whitespace)\n     * @param state Current parser state\n     * @returns true if the rule should be attempted, false to skip\n     */\n    _qualify?:\n      | string[]\n      | ((source: string, state: MarkdownToJSX.State) => boolean)\n    _render?: (\n      node: ParserOutput,\n      /**\n       * Continue rendering AST nodes if applicable.\n       */\n      render: RuleOutput,\n      state?: MarkdownToJSX.State\n    ) => React.ReactNode\n  }\n\n  export type Rules = {\n    [K in ParserResult['type']]: K extends typeof RuleType.table\n      ? Rule<Extract<ParserResult, { type: K | typeof RuleType.paragraph }>>\n      : Rule<Extract<ParserResult, { type: K }>>\n  }\n\n  export type Override =\n    | RequireAtLeastOne<{\n        component: React.ElementType\n        props: Object\n      }>\n    | React.ElementType\n\n  export type Overrides = {\n    [tag in HTMLTags]?: Override\n  } & {\n    [customComponent: string]: Override\n  }\n\n  export type Options = Partial<{\n    /**\n     * Ultimate control over the output of all rendered JSX.\n     */\n    createElement: (\n      tag: Parameters<CreateElement>[0],\n      props: React.JSX.IntrinsicAttributes,\n      ...children: React.ReactNode[]\n    ) => React.ReactNode\n\n    /**\n     * The library automatically generates an anchor tag for bare URLs included in the markdown\n     * document, but this behavior can be disabled if desired.\n     */\n    disableAutoLink: boolean\n\n    /**\n     * Disable the compiler's best-effort transcription of provided raw HTML\n     * into JSX-equivalent. This is the functionality that prevents the need to\n     * use `dangerouslySetInnerHTML` in React.\n     */\n    disableParsingRawHTML: boolean\n\n    /**\n     * Forces the compiler to have space between hash sign and the header text which\n     * is explicitly stated in the most of the markdown specs.\n     * https://github.github.com/gfm/#atx-heading\n     * `The opening sequence of # characters must be followed by a space or by the end of line.`\n     */\n    enforceAtxHeadings: boolean\n\n    /**\n     * Forces the compiler to always output content with a block-level wrapper\n     * (`<p>` or any block-level syntax your markdown already contains.)\n     */\n    forceBlock: boolean\n\n    /**\n     * Forces the compiler to always output content with an inline wrapper (`<span>`)\n     */\n    forceInline: boolean\n\n    /**\n     * Forces the compiler to wrap results, even if there is only a single\n     * child or no children.\n     */\n    forceWrapper: boolean\n\n    /**\n     * Supply additional HTML entity: unicode replacement mappings.\n     *\n     * Pass only the inner part of the entity as the key,\n     * e.g. `&le;` -> `{ \"le\": \"\\u2264\" }`\n     *\n     * By default\n     * the following entities are replaced with their unicode equivalents:\n     *\n     * ```\n     * &amp;\n     * &apos;\n     * &gt;\n     * &lt;\n     * &nbsp;\n     * &quot;\n     * ```\n     */\n    namedCodesToUnicode: {\n      [key: string]: string\n    }\n\n    /**\n     * Selectively control the output of particular HTML tags as they would be\n     * emitted by the compiler.\n     */\n    overrides: Overrides\n\n    /**\n     * Allows for full control over rendering of particular rules.\n     * For example, to implement a LaTeX renderer such as `react-katex`:\n     *\n     * ```\n     * renderRule(next, node, renderChildren, state) {\n     *   if (node.type === RuleType.codeBlock && node.lang === 'latex') {\n     *     return (\n     *       <TeX as=\"div\" key={state.key}>\n     *         {String.raw`${node.text}`}\n     *       </TeX>\n     *     )\n     *   }\n     *\n     *   return next();\n     * }\n     * ```\n     *\n     * Thar be dragons obviously, but you can do a lot with this\n     * (have fun!) To see how things work internally, check the `render`\n     * method in source for a particular rule.\n     */\n    renderRule: (\n      /** Resume normal processing, call this function as a fallback if you are not returning custom JSX. */\n      next: () => React.ReactNode,\n      /** the current AST node, use `RuleType` against `node.type` for identification */\n      node: ParserResult,\n      /** use as `renderChildren(node.children)` for block nodes */\n      renderChildren: RuleOutput,\n      /** contains `key` which should be supplied to the topmost JSX element */\n      state: State\n    ) => React.ReactNode\n\n    /**\n     * Override the built-in sanitizer function for URLs, etc if desired. The built-in version is available as a library export called `sanitizer`.\n     */\n    sanitizer: (\n      value: string,\n      tag: HTMLTags,\n      attribute: string\n    ) => string | null\n\n    /**\n     * Override normalization of non-URI-safe characters for use in generating\n     * HTML IDs for anchor linking purposes.\n     */\n    slugify: (input: string, defaultFn: (input: string) => string) => string\n\n    /**\n     * Declare the type of the wrapper to be used when there are multiple\n     * children to render. Set to `null` to get an array of children back\n     * without any wrapper, or use `React.Fragment` to get a React element\n     * that won't show up in the DOM.\n     */\n    wrapper: React.ElementType | null\n  }>\n}\n\nexport default Markdown\n"], "names": ["RuleType", "blockQuote", "breakLine", "breakThematic", "codeBlock", "codeFenced", "codeInline", "footnote", "footnoteReference", "gfmTask", "heading", "headingSetext", "htmlBlock", "htmlComment", "htmlSelfClosing", "image", "link", "linkAngleBraceStyleDetector", "linkBareUrlDetector", "linkMailtoDetector", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedList", "paragraph", "ref", "refImage", "refLink", "table", "tableSeparator", "text", "textBolded", "textEmphasized", "textEscaped", "textMarked", "textStrikethroughed", "unorderedList", "ATTRIBUTE_TO_JSX_PROP_MAP", "reduce", "obj", "x", "toLowerCase", "class", "for", "namedCodesToUnicode", "amp", "apos", "gt", "lt", "nbsp", "quot", "DO_NOT_PROCESS_HTML_ELEMENTS", "ATTRIBUTES_TO_SANITIZE", "ATTR_EXTRACTOR_R", "AUTOLINK_MAILTO_CHECK_R", "BLOCK_END_R", "BLOCKQUOTE_R", "BLOCKQUOTE_TRIM_LEFT_MULTILINE_R", "BLOCKQUOTE_ALERT_R", "BREAK_LINE_R", "BREAK_THEMATIC_R", "CODE_BLOCK_FENCED_R", "CODE_BLOCK_R", "CODE_INLINE_R", "CONSECUTIVE_NEWLINE_R", "CR_NEWLINE_R", "FOOTNOTE_R", "FOOTNOTE_REFERENCE_R", "FORMFEED_R", "FRONT_MATTER_R", "GFM_TASK_R", "HEADING_R", "HEADING_ATX_COMPLIANT_R", "HEADING_SETEXT_R", "HTML_BLOCK_ELEMENT_R", "HTML_CHAR_CODE_R", "HTML_COMMENT_R", "HTML_CUSTOM_ATTR_R", "HTML_SELF_CLOSING_ELEMENT_R", "INTERPOLATION_R", "LINK_AUTOLINK_BARE_URL_R", "LINK_AUTOLINK_MAILTO_R", "LINK_AUTOLINK_R", "CAPTURE_LETTER_AFTER_HYPHEN", "NP_TABLE_R", "PARAGRAPH_R", "REFERENCE_IMAGE_OR_LINK", "REFERENCE_IMAGE_R", "REFERENCE_LINK_R", "SHOULD_RENDER_AS_BLOCK_R", "TAB_R", "TABLE_TRIM_PIPES", "TABLE_CENTER_ALIGN", "TABLE_LEFT_ALIGN", "TABLE_RIGHT_ALIGN", "INLINE_SKIP_R", "TEXT_BOLD_R", "RegExp", "TEXT_EMPHASIZED_R", "TEXT_MARKED_R", "TEXT_STRIKETHROUGHED_R", "SHORTCODE_R", "TEXT_ESCAPED_R", "UNESCAPE_R", "TEXT_PLAIN_R", "TRIM_STARTING_NEWLINES", "HTML_LEFT_TRIM_AMOUNT_R", "LIST_LOOKBEHIND_R", "ORDERED_LIST_BULLET", "UNORDERED_LIST_BULLET", "generateListItemPrefix", "type", "ORDERED_LIST_ITEM_PREFIX", "UNORDERED_LIST_ITEM_PREFIX", "generateListItemPrefixRegex", "ORDERED_LIST_ITEM_PREFIX_R", "UNORDERED_LIST_ITEM_PREFIX_R", "generateListItemRegex", "ORDERED_LIST_ITEM_R", "UNORDERED_LIST_ITEM_R", "generateListRegex", "bullet", "ORDERED_LIST_R", "UNORDERED_LIST_R", "generateListRule", "h", "ordered", "LIST_R", "LIST_ITEM_R", "LIST_ITEM_PREFIX_R", "_qualify", "source", "test", "_match", "allowInline", "state", "isStartOfLine", "exec", "prevCapture", "list", "inline", "simple", "_order", "_parse", "capture", "parse", "start", "undefined", "items", "replace", "match", "lastItemWasAParagraph", "map", "item", "i", "space", "length", "spaceRegex", "content", "isLastItem", "thisItemIsAParagraph", "indexOf", "oldStateInline", "oldStateList", "adjustedContent", "trimEnd", "result", "_render", "node", "output", "key", "LINK_R", "IMAGE_R", "str", "end", "slice", "startsWith", "prefix", "qualifies", "qualify", "Array", "isArray", "slugify", "parseTableAlignCapture", "alignCapture", "parseTableRow", "tableOutput", "prevInTable", "inTable", "cells", "acc", "flush", "cell", "push", "apply", "trim", "split", "filter", "Boolean", "for<PERSON>ach", "fragment", "arr", "parseTable", "align", "rowText", "parseTableCells", "header", "children", "getTableStyle", "colIndex", "textAlign", "fn", "inlineRegex", "regex", "simpleInlineRegex", "blockRegex", "anyScopeRegex", "SANITIZE_R", "sanitizer", "input", "decoded", "decodeURIComponent", "e", "unescape", "rawString", "parseInline", "isCurrentlyInline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSimpleInline", "parseBlock", "parseCaptureInline", "captureNothing", "renderNothing", "cx", "args", "join", "get", "src", "path", "fb", "ptr", "frags", "shift", "compiler", "markdown", "options", "overrides", "_extends", "slug", "sanitize", "createElement", "React", "NON_PARAGRAPH_BLOCK_SYNTAXES", "enforceAtxHeadings", "BLOCK_SYNTAXES", "tag", "props", "overrideProps", "override", "getTag", "className", "compile", "forceInline", "forceBlock", "emitter", "parser", "pop", "wrapper", "jsx", "forceWrapper", "attrStringToMap", "attributes", "raw", "delimiterIdx", "_", "letter", "toUpperCase", "normalizeAttributeKey", "value", "first", "unquote", "<PERSON><PERSON><PERSON>", "normalizedValue", "sanitizeUrlFn", "styleString", "styles", "buffer", "inUrl", "inQuotes", "quoteChar", "char", "endsWith", "declaration", "colonIndex", "parseStyleAttribute", "substr", "attributeValueToJSXPropValue", "footnotes", "refs", "rules", "alert", "unshift", "attrs", "noInnerParse", "__", "lang", "identifier", "target", "href", "completed", "checked", "readOnly", "id", "level", "whitespace", "trimmer", "trimmed", "parseFunc", "some", "r", "tagName", "ast", "inAnchor", "prevInHTML", "inHTML", "alt", "title", "disableAutoLink", "address", "every", "line", "captured", "fallback<PERSON><PERSON><PERSON><PERSON>", "style", "row", "c", "ret", "full", "inner", "disableParsingRawHTML", "ruleList", "Object", "keys", "nestedParse", "ruleType", "rule", "substring", "parsed", "sort", "a", "b", "normalizeWhitespace", "parser<PERSON><PERSON>", "render", "userRender", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "renderRule", "patchedRender", "<PERSON><PERSON><PERSON>", "lastWasString", "nodeOut", "isString", "def", "_ref", "_objectWithoutPropertiesLoose", "_excluded", "cloneElement"], "mappings": "2RAcaA,EAAW,CACtBC,WAAY,IACZC,UAAW,IACXC,cAAe,IACfC,UAAW,IACXC,WAAY,IACZC,WAAY,IACZC,SAAU,IACVC,kBAAmB,IACnBC,QAAS,IACTC,QAAS,IACTC,cAAe,KAEfC,UAAW,KACXC,YAAa,KAEbC,gBAAiB,KACjBC,MAAO,KACPC,KAAM,KAENC,4BAA6B,KAE7BC,oBAAqB,KAErBC,mBAAoB,KACpBC,iBAAkB,KAClBC,YAAa,KACbC,UAAW,KACXC,IAAK,KACLC,SAAU,KACVC,QAAS,KACTC,MAAO,KACPC,eAAgB,KAChBC,KAAM,KACNC,WAAY,KACZC,eAAgB,KAChBC,YAAa,KACbC,WAAY,KACZC,oBAAqB,KACrBC,cAAe,MAiCXC,EAA4B,CAChC,kBACA,oBACA,eACA,YACA,WACA,cACA,cACA,UACA,UACA,UACA,kBACA,cACA,cACA,UACA,aACA,cACA,aACA,iBACA,aACA,cACA,WACA,YACA,YACA,UACA,eACA,cACA,YACA,aACA,YACA,aACA,aACA,WACA,UACA,aACA,SACA,UACA,SACA,WACA,UACAC,OACA,CAACC,EAAKC,KACJD,EAAIC,EAAEC,eAAiBD,EAChBD,GAET,CAAEG,MAAO,YAAaC,IAAK,YAGvBC,EAAsB,CAC1BC,IAAK,IACLC,KAAM,IACNC,GAAI,IACJC,GAAI,IACJC,KAAM,IACNC,KAAM,KAGFC,EAA+B,CAAC,QAAS,SAAU,OACnDC,EAAyB,CAC7B,MACA,OACA,OACA,aACA,SACA,UAmCIC,EACJ,+GAIIC,EAA0B,WAC1BC,EAAc,UACdC,EAAe,4BACfC,EAAmC,WACnCC,EAAqB,iCACrBC,EAAe,WACfC,EAAmB,kCACnBC,EACJ,uEACIC,EAAe,kCACfC,EAAgB,iCAChBC,EAAwB,eACxBC,EAAe,SAkCfC,EAAa,sDAEbC,EAAuB,iBACvBC,EAAa,MACbC,EAAiB,mCACjBC,EAAa,kBACbC,EAAY,mDACZC,EACJ,mDACIC,EAAmB,6BAwBnBC,EACJ,wIAEIC,EAAmB,iDAEnBC,EAAiB,uBAKjBC,EAAqB,oCAErBC,EACJ,wEACIC,EAAkB,WAClBC,EAA2B,uCAC3BC,EAAyB,qBACzBC,EAAkB,uBAClBC,EAA8B,cAC9BC,EAAa,8DACbC,EAAc,yBACdC,EAA0B,gDAC1BC,EAAoB,+BACpBC,EAAmB,8BACnBC,EAA2B,qCAC3BC,EAAQ,MACRC,EAAmB,iBACnBC,EAAqB,aACrBC,EAAmB,YACnBC,EAAoB,YAOpBC,EACJ,2EAMIC,EAAkBC,oBAAoBF,kBAKtCG,EAAwBD,iBAAiBF,eAKzCI,EAAoBF,eAAeF,QAKnCK,GAA6BH,eAAeF,QAK5CM,GAAc,sBAEdC,GAAiB,sBACjBC,GAAa,sBAMbC,GAAe,uDAEfC,GAAyB,OAEzBC,GAA0B,YAM1BC,GAAoB,gBAGpBC,GAAsB,cACtBC,GAAwB,YAE9B,SAASC,GAAuBC,GAC9B,MACE,SAXuB,IAYtBA,EAAmBH,GAAsBC,IAC1C,KAEJ,CAIA,MAAMG,GAA2BF,GAnBN,GAoBrBG,GAA6BH,GAnBN,GAqB7B,SAASI,GAA4BH,GACnC,OAAWd,OACT,KAxBuB,IAyBpBc,EAAmBC,GAA2BC,IAErD,CAEA,MAAME,GAA6BD,GA7BR,GA8BrBE,GAA+BF,GA7BR,GA+B7B,SAASG,GAAsBN,GAQ7B,OAAWd,OACT,KAzCuB,IA0CpBc,EACGC,GACAC,IAHN,uBAzCuB,IA+CpBF,EAAmBH,GAAsBC,IAC1C,qBACF,KAEJ,CAEA,MAAMS,GAAsBD,GArDD,GAsDrBE,GAAwBF,GArDD,GAyD7B,SAASG,GAAkBT,GACzB,MAAMU,EA3DmB,IA2DVV,EAAmBH,GAAsBC,GAExD,OAAWZ,OACT,SACEwB,EADF,oCAKEA,EACA,OACAA,EAPF,qBAaJ,CAEA,MAAMC,GAAiBF,GA7EI,GA8ErBG,GAAmBH,GA7EI,GA+E7B,SAASI,GACPC,EACAd,GAIA,MAAMe,EAtFmB,IAsFTf,EACVgB,EAASD,EAAUJ,GAAiBC,GACpCK,EAAcF,EAAUR,GAAsBC,GAC9CU,EAAqBH,EACvBX,GACAC,GAEJ,MAAO,CACLc,EAAUC,GAAUF,EAAmBG,KAAKD,GAC5CE,EAAQC,GAAY,SAAUH,EAAQI,GASpC,MAAMC,EAAgB7B,GAAkB8B,KAAKF,EAAMG,aAGnD,OAAIF,IAFkBD,EAAMI,OAAUJ,EAAMK,SAAWL,EAAMM,QAKpDd,EAAOU,KAFdN,EAASK,EAAc,GAAKL,OAMhC,GACAW,EAzYI,EA0YJC,EAAOC,EAASC,EAAOV,GACrB,MACMW,EAAQpB,GADCkB,EAAQ,QACWG,EAC5BC,EAAQJ,EAAQ,GAGnBK,QAAQ9F,EAAa,MACrB+F,MAAMtB,GAET,IAAIuB,GAAwB,EAgE5B,MAAO,CACLH,MA/DkBA,EAAMI,IAAI,SAAUC,EAAMC,GAE5C,MAAMC,EAAQ1B,EAAmBQ,KAAKgB,GAAM,GAAGG,OAIzCC,EAAiB5D,OAAO,QAAU0D,EAAQ,IAAK,MAG/CG,EAAUL,EAEbJ,QAAQQ,EAAY,IAEpBR,QAAQpB,EAAoB,IASzB8B,EAAaL,IAAMN,EAAMQ,OAAS,EASlCI,GAR8C,IAA7BF,EAAQG,QAAQ,SASlBF,GAAcR,EACnCA,EAAwBS,EAKxB,MAAME,EAAiB3B,EAAMK,OACvBuB,EAAe5B,EAAMI,KAK3B,IAAIyB,EAJJ7B,EAAMI,MAAO,EAKTqB,GACFzB,EAAMK,QAAS,EACfwB,EAAkBC,GAAQP,GAAW,SAErCvB,EAAMK,QAAS,EACfwB,EAAkBC,GAAQP,IAG5B,MAAMQ,EAASrB,EAAMmB,EAAiB7B,GAMtC,OAHAA,EAAMK,OAASsB,EACf3B,EAAMI,KAAOwB,EAENG,CACT,GAIExC,QAASA,EACToB,MAAOA,EAEX,EACAqB,EAAOA,CAACC,EAAMC,EAAQlC,IAIlBV,EAHU2C,EAAK1C,QAAU,KAAO,MAI9B4C,IAAKnC,EAAMmC,IACXxB,MAAqBhJ,OAAdsK,EAAKzD,KAAgCyD,EAAKtB,WAAQC,GAExDqB,EAAKpB,MAAMI,IAAI,SAA0BC,EAAMC,GAC9C,OAAO7B,QAAI6C,IAAKhB,GAAIe,EAAOhB,EAAMlB,GACnC,IAKV,CAEA,MAGMoC,GAAa1E,OACjB,gKAEI2E,GAAU,0DAEhB,SAASP,GAAQQ,GACf,IAAIC,EAAMD,EAAIjB,OACd,KAAOkB,EAAM,GAAKD,EAAIC,EAAM,IAAM,KAAKA,IACvC,OAAOD,EAAIE,MAAM,EAAGD,EACtB,CAEA,SAASE,GAAWH,EAAaI,GAC/B,OAAOJ,EAAIG,WAAWC,EACxB,CAEA,SAASC,GACP/C,EACAI,EACA4C,GAEA,GAAIC,MAAMC,QAAQF,GAAU,CAC1B,IAAK,IAAIzB,EAAI,EAAGA,EAAIyB,EAAQvB,OAAQF,IAClC,GAAIsB,GAAW7C,EAAQgD,EAAQzB,IAAK,SAGtC,QACF,CAEA,OAAOyB,EAAQhD,EAAQI,EACzB,UAiBgB+C,GAAQT,GACtB,OAAOA,EACJxB,QAAQ,oBAAqB,KAC7BA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,cAAe,KACvBA,QAAQ,cAAe,KACvBA,QAAQ,QAAS,KACjBA,QAAQ,kBAAmB,KAC3BA,QAAQ,cAAe,KACvBA,QAAQ,UAAW,KACnBA,QAAQ,gBAAiB,IACzBA,QAAQ,MAAO,KACf5G,aACL,CAEA,SAAS8I,GAAuBC,GAC9B,OAAI1F,EAAkBsC,KAAKoD,GAClB,QACE5F,EAAmBwC,KAAKoD,GAC1B,SACE3F,EAAiBuC,KAAKoD,GACxB,WAIX,CAEA,SAASC,GACPtD,EACAc,EACAV,EACAmD,GAEA,MAAMC,EAAcpD,EAAMqD,QAE1BrD,EAAMqD,SAAU,EAEhB,IAAIC,EAAwC,CAAC,IACzCC,EAAM,GAEV,SAASC,IACP,IAAKD,EAAK,OAEV,MAAME,EAAOH,EAAMA,EAAMjC,OAAS,GAClCoC,EAAKC,KAAKC,MAAMF,EAAM/C,EAAM6C,EAAKvD,IACjCuD,EAAM,EACR,CA4BA,OA1BA3D,EACGgE,OAEAC,MAAM,qBACNC,OAAOC,SACPC,QAAQ,CAACC,EAAU9C,EAAG+C,KACG,MAApBD,EAASL,SACXJ,IAEIL,GACQ,IAANhC,GAAWA,IAAM+C,EAAI7C,OAAS,GAEhCiC,EAAMI,KAAK,IAOjBH,GAAOU,IAGXT,IAEAxD,EAAMqD,QAAUD,EAETE,CACT,CAoBA,SAASa,GACP1D,EACAC,EACAV,GAMAA,EAAMK,QAAS,EACf,MAAM+D,EAAQ3D,EAAQ,GAAqBA,EAAQ,GA3B1BK,QAAQ1D,EAAkB,IAAIyG,MAAM,KAE5C5C,IAAI+B,IAyBoC,GACnDM,EAAQ7C,EAAQ,GAvBxB,SACEb,EACAc,EACAV,GAIA,OAFiBJ,EAAOgE,OAAOC,MAAM,MAErB5C,IAAI,SAAUoD,GAC5B,OAAOnB,GAAcmB,EAAS3D,EAAOV,GAAO,EAC9C,EACF,CAa6BsE,CAAgB7D,EAAQ,GAAIC,EAAOV,GAAS,GACjEuE,EAASrB,GAAczC,EAAQ,GAAIC,EAAOV,IAASsD,EAAMjC,QAG/D,OAFArB,EAAMK,QAAS,EAERiD,EAAMjC,OACT,CACE+C,MAAOA,EACPd,MAAOA,EACPiB,OAAQA,EACR/F,KAAM7G,MAER,CACE6M,SAAUD,EACV/F,KAAM7G,KAEd,CAEA,SAAS8M,GAAcxC,EAAMyC,GAC3B,OAA+B,MAAxBzC,EAAKmC,MAAMM,GACd,GACA,CACEC,UAAW1C,EAAKmC,MAAMM,GAE9B,CAuNA,SAAS3E,GAAqD6E,GAG5D,OAFAA,EAAGvE,OAAS,EAELuE,CACT,CAGA,SAASC,GAAYC,GACnB,OAAO/E,GAAY,SAAeH,EAAQI,GACxC,OAAIA,EAAMK,OACDyE,EAAM5E,KAAKN,OAItB,EACF,CAGA,SAASmF,GAAkBD,GACzB,OAAO/E,GAAY,SACjBH,EACAI,GAEA,OAAIA,EAAMK,QAAUL,EAAMM,OACjBwE,EAAM5E,KAAKN,OAItB,EACF,CAGA,SAASoF,GAAWF,GAClB,gBAAsBlF,EAAgBI,GACpC,OAAIA,EAAMK,QAAUL,EAAMM,YAGjBwE,EAAM5E,KAAKN,EAEtB,CACF,CAGA,SAASqF,GAAcH,GACrB,OAAO/E,GAAY,SAAeH,GAChC,OAAOkF,EAAM5E,KAAKN,EACpB,EACF,CAEA,MAAMsF,GAAa,kDAEHC,GAAUC,GACxB,IACE,MAAMC,EAAUC,mBAAmBF,GAAOtE,QAAQ,kBAAmB,IAErE,GAAIoE,GAAWrF,KAAKwF,GAQlB,WAcJ,CAZE,MAAOE,GAWP,WACF,CAEA,OAAOH,CACT,CAEA,SAASI,GAASC,GAChB,OAAOA,EAAYA,EAAU3E,QAAQ9C,GAAY,MAAQyH,CAC3D,CAKA,SAASC,GACPhF,EACA8D,EACAxE,GAEA,MAAM2F,EAAoB3F,EAAMK,SAAU,EACpCuF,EAAoB5F,EAAMM,SAAU,EAC1CN,EAAMK,QAAS,EACfL,EAAMM,QAAS,EACf,MAAMyB,EAASrB,EAAM8D,EAAUxE,GAG/B,OAFAA,EAAMK,OAASsF,EACf3F,EAAMM,OAASsF,EACR7D,CACT,CAKA,SAAS8D,GACPnF,EACA8D,EACAxE,GAEA,MAAM2F,EAAoB3F,EAAMK,SAAU,EACpCuF,EAAoB5F,EAAMM,SAAU,EAC1CN,EAAMK,QAAS,EACfL,EAAMM,QAAS,EACf,MAAMyB,EAASrB,EAAM8D,EAAUxE,GAG/B,OAFAA,EAAMK,OAASsF,EACf3F,EAAMM,OAASsF,EACR7D,CACT,CAEA,SAAS+D,GACPpF,EACA8D,EACAxE,GAEA,MAAM2F,EAAoB3F,EAAMK,SAAU,EAC1CL,EAAMK,QAAS,EACf,MAAM0B,EAASrB,EAAM8D,EAAUxE,GAE/B,OADAA,EAAMK,OAASsF,EACR5D,CACT,CAEA,MAAMgE,GAEDA,CAACtF,EAASC,EAAOV,KACb,CACLwE,SAAUkB,GAAYhF,EAAOD,EAAQ,GAAIT,KAI7C,SAASgG,KACP,MAAO,EACT,CAEA,SAASC,KACP,WACF,CAwDA,SAASC,MAAMC,GACb,OAAOA,EAAKrC,OAAOC,SAASqC,KAAK,IACnC,CAEA,SAASC,GAAIC,EAAaC,EAAcC,GACtC,IAAIC,EAAMH,EACV,MAAMI,EAAQH,EAAK1C,MAAM,KAEzB,KAAO6C,EAAMrF,SACXoF,EAAMA,EAAIC,EAAM,SAEJ9F,IAAR6F,IACCC,EAAMC,QAGb,OAAOF,GAAOD,CAChB,UAagBI,GACdC,EAAmB,GACnBC,EAAiC,IAEjCA,EAAQC,UAAYD,EAAQC,WAAa,GACzCD,EAAQzM,oBAAsByM,EAAQzM,oBAAmB2M,KAChD3M,EAAwByM,EAAQzM,qBACrCA,EAEJ,MAAM4M,EAAOH,EAAQ/D,SAAWA,GAC1BmE,EAAWJ,EAAQ3B,WAAaA,GAChCgC,EAAgBL,EAAQK,eAAiBC,EAAMD,cAE/CE,EAA+B,CACnCpM,EACAK,EACAC,EACAuL,EAAQQ,mBAAqBrL,EAA0BD,EACvDE,EACAW,EACAsC,GACAC,IAGImI,EAAiB,IAClBF,EACHvK,EACAX,EACAE,EACAE,GA6CF,SAAS+C,EAEPkI,EACAC,KAIGjD,GAEH,MAAMkD,EAAgBrB,GAAIS,EAAQC,UAAcS,WAAa,IAE7D,OAAOL,EAhGX,SAAgBK,EAAaT,GAC3B,MAAMY,EAAWtB,GAAIU,EAAWS,GAEhC,OAAKG,EAEsB,mBAAbA,GACS,iBAAbA,GAAyB,WAAYA,EAC3CA,EACAtB,GAAIU,EAAcS,eAAiBA,GALjBA,CAMxB,CAwFMI,CAAOJ,EAAKV,EAAQC,WAAUC,KAEzBS,EACAC,GACHG,UAAW3B,SAAGuB,SAAAA,EAAOI,UAAWH,EAAcG,iBAAcjH,OAE3D4D,EAEP,CAEA,SAASsD,GAAQ1C,GACfA,EAAQA,EAAMtE,QAAQhF,EAAgB,IAEtC,IAAIuE,GAAS,EAETyG,EAAQiB,YACV1H,GAAS,EACCyG,EAAQkB,aAKlB3H,GAAkD,IAAzCnD,EAAyB2C,KAAKuF,IAGzC,MAAMlB,EAAM+D,GACVC,GACE7H,EACI+E,EACGtD,GAAQsD,GAAOtE,QAAQ5C,GAAwB,WACtD,CACEmC,YAKN,KACiC,iBAAxB6D,EAAIA,EAAI7C,OAAS,KACvB6C,EAAIA,EAAI7C,OAAS,GAAGuC,QAErBM,EAAIiE,MAGN,GAAwB,OAApBrB,EAAQsB,QACV,OAAOlE,EAGT,MAAMkE,EAAUtB,EAAQsB,UAAY/H,EAAS,OAAS,OACtD,IAAIgI,EAEJ,GAAInE,EAAI7C,OAAS,GAAKyF,EAAQwB,aAC5BD,EAAMnE,UACkB,IAAfA,EAAI7C,OAIb,OAHAgH,EAAMnE,EAAI,GAGS,iBAARmE,EACF/I,UAAM6C,IAAI,SAASkG,GAEnBA,EAITA,EAAM,IACR,CAEA,OAAOlB,EAAciB,EAAS,CAAEjG,IAAK,SAAWkG,EAClD,CAEA,SAASE,GACPf,EACAlF,GAEA,IAAKA,IAAQA,EAAIsB,OACf,YAGF,MAAM4E,EAAalG,EAAIvB,MAAMjG,GAC7B,OAAK0N,EAIEA,EAAWzO,OAAO,SAAUkH,EAAKwH,GACtC,MAAMC,EAAeD,EAAI/G,QAAQ,KAEjC,IAAsB,IAAlBgH,EAAqB,CACvB,MAAMvG,EAzmBd,SAA+BA,GAS7B,OANqB,IAFDA,EAAIT,QAAQ,MAE4B,OAAlCS,EAAIpB,MAAMzE,KAClC6F,EAAMA,EAAIrB,QAAQlE,EAA6B,SAAU+L,EAAGC,GAC1D,OAAOA,EAAOC,aAChB,IAGK1G,CACT,CA+lBoB2G,CAAsBL,EAAIjG,MAAM,EAAGkG,IAAe9E,OACxDmF,EA7vBd,SAAiBzG,GACf,MAAM0G,EAAQ1G,EAAI,GAClB,OACa,MAAV0G,GAA2B,MAAVA,IAClB1G,EAAIjB,QAAU,GACdiB,EAAIA,EAAIjB,OAAS,KAAO2H,EAEjB1G,EAAIE,MAAM,GAAI,GAEhBF,CACT,CAmvBsB2G,CAAQR,EAAIjG,MAAMkG,EAAe,GAAG9E,QAE5CsF,EAAYpP,EAA0BqI,IAAQA,EAGpD,GAAkB,QAAd+G,EAAqB,OAAOjI,EAEhC,MAAMkI,EAAmBlI,EAAIiI,GAtiBrC,SACE1B,EACArF,EACA4G,EACAK,GAEA,MAAY,UAARjH,EAnEN,SAA6BkH,GAC3B,MAAMC,EAAuB,GAC7B,IAAIC,EAAS,GACTC,GAAQ,EACRC,GAAW,EACXC,EAA4B,GAEhC,IAAKL,EAAa,OAAOC,EAEzB,IAAK,IAAInI,EAAI,EAAGA,EAAIkI,EAAYhI,OAAQF,IAAK,CAC3C,MAAMwI,EAAON,EAAYlI,GAqBzB,GAlBc,MAATwI,GAAyB,MAATA,GAAkBH,IAChCC,EAGME,IAASD,IAClBD,GAAW,EACXC,EAAY,KAJZD,GAAW,EACXC,EAAYC,IAQH,MAATA,GAAgBJ,EAAOK,SAAS,OAClCJ,GAAQ,EACU,MAATG,GAAgBH,IACzBA,GAAQ,GAIG,MAATG,GAAiBF,GAAaD,EAYhCD,GAAUI,MAZ6B,CACvC,MAAME,EAAcN,EAAO3F,OAC3B,GAAIiG,EAAa,CACf,MAAMC,EAAaD,EAAYnI,QAAQ,KACvC,GAAIoI,EAAa,EAAG,CAClB,MAAM3H,EAAM0H,EAAYrH,MAAM,EAAGsH,GAAYlG,OACvCmF,EAAQc,EAAYrH,MAAMsH,EAAa,GAAGlG,OAChD0F,EAAO5F,KAAK,CAACvB,EAAK4G,GACpB,CACF,CACAQ,EAAS,EACX,CAGF,CAGA,MAAMM,EAAcN,EAAO3F,OAC3B,GAAIiG,EAAa,CACf,MAAMC,EAAaD,EAAYnI,QAAQ,KACvC,GAAIoI,EAAa,EAAG,CAClB,MAAM3H,EAAM0H,EAAYrH,MAAM,EAAGsH,GAAYlG,OACvCmF,EAAQc,EAAYrH,MAAMsH,EAAa,GAAGlG,OAChD0F,EAAO5F,KAAK,CAACvB,EAAK4G,GACpB,CACF,CAEA,OAAOO,CACT,CASWS,CAAoBhB,GAAOhP,OAAO,SAAUuP,GAASnH,EAAK4G,IAU/D,OAFAO,EALsBnH,EAAIrB,QAAQ,YAAakJ,GAC7CA,EAAO,GAAGnB,gBAIYO,EAAcL,EAAOvB,EAAKrF,GAE3CmH,CACT,EAAG,KAC+C,IAAzCzO,EAAuB6G,QAAQS,GACjCiH,EAAc5D,GAASuD,GAAQvB,EAAKrF,IAClC4G,EAAMhI,MAAMvE,KAErBuM,EAAQvD,GAASuD,EAAMvG,MAAM,EAAGuG,EAAM1H,OAAS,KAGnC,SAAV0H,GAEiB,UAAVA,GAIJA,EACT,CAqgBkDkB,CACxCzC,EACArF,EACA4G,EACA7B,GAI2B,iBAApBiC,IACNhN,EAAqB0D,KAAKsJ,IACzB5M,EAA4BsD,KAAKsJ,MAEnClI,EAAIiI,GAAapB,GAAQqB,EAAgBvF,QAE7C,KAAmB,UAAR6E,IACTxH,EAAInH,EAA0B2O,IAAQA,IAAO,GAG/C,OAAOxH,CACT,EAAG,QACL,CAsBA,MAAMiJ,GAAwD,GACxDC,GAA6D,GAQ7DC,GAA6B,CACjC,EAAuB,CACrBzK,EAAU,CAAC,KACXG,EAAQkF,GAAW/J,GACnBsF,EAx0CE,EAy0CFC,EAAOC,EAASC,EAAOV,GACrB,OAASqK,EAAO9I,GAAWd,EAAQ,GAChCK,QAAQ5F,EAAkC,IAC1C6F,MAAM5F,GAET,MAAO,CACLkP,QACA7F,SAAU9D,EAAMa,EAASvB,GAE7B,EACAgC,EAAQC,EAAMC,EAAQlC,GACpB,MAAMyH,EAAQ,CACZtF,IAAKnC,EAAMmC,KAgBb,OAbIF,EAAKoI,QACP5C,EAAMI,UACJ,kBAAoBZ,EAAKhF,EAAKoI,MAAMnQ,cAAe6I,IAErDd,EAAKuC,SAAS8F,QAAQ,CACpBC,MAAO,GACP/F,SAAU,CAAC,CAAEhG,KAAM7G,KAAe4B,KAAM0I,EAAKoI,QAC7CG,cAAc,EACdhM,KAAM7G,KACN6P,IAAK,YAIFlI,EAAE,aAAcmI,EAAOvF,EAAOD,EAAKuC,SAAUxE,GACtD,GAGF,EAAsB,CACpBF,EAAQmF,GAAc7J,GACtBmF,EA32CE,EA42CFC,EAAQwF,GACRhE,EAAOA,CAAC2G,EAAG8B,EAAIzK,IACNV,QAAI6C,IAAKnC,EAAMmC,OAI1B,EAA0B,CACxBxC,EAAUC,IACR,MAAM+J,EAAO/J,EAAO,GACpB,MAAgB,MAAT+J,GAAyB,MAATA,GAAyB,MAATA,CAAS,EAElD7J,EAAQkF,GAAW3J,GACnBkF,EAx3CE,EAy3CFC,EAAQwF,GACRhE,EAAOA,CAAC2G,EAAG8B,EAAIzK,IACNV,QAAI6C,IAAKnC,EAAMmC,OAI1B,EAAsB,CACpBxC,EAAU,CAAC,QACXG,EAAQkF,GAAWzJ,GACnBgF,EAt4CC,EAu4CDC,EAAOC,IACE,CACLiK,UAAM9J,EACNrH,KAAMiM,GAAS1D,GAAQrB,EAAQ,GAAGK,QAAQ,UAAW,QAIzDkB,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,SAAK6C,IAAKnC,EAAMmC,KACd7C,cACM2C,EAAKsI,OACT1C,UAAW5F,EAAKyI,aAAezI,EAAKyI,KAAS,KAE5CzI,EAAK1I,QAWhB,EAAuB,CACrBoG,EAAU,CAAC,MAAO,OAClBG,EAAQkF,GAAW1J,GACnBiF,EAn6CC,EAo6CDC,EAAOC,IACE,CAEL8J,MAAOhC,GAAgB,OAAQ9H,EAAQ,IAAM,IAC7CiK,KAAMjK,EAAQ,SAAMG,EACpBrH,KAAMkH,EAAQ,GACdjC,KAAM7G,OAKZ,EAAuB,CACrBgI,EAAU,CAAC,KACXG,EAAQiF,GAAkBvJ,GAC1B+E,EAt6CC,EAu6CDC,EAAOC,IACE,CACLlH,KAAMiM,GAAS/E,EAAQ,MAG3BuB,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,UAAM6C,IAAKnC,EAAMmC,KAAMF,EAAK1I,OAOvC,EAAqB,CACnBoG,EAAU,CAAC,MACXG,EAAQkF,GAAWrJ,GACnB4E,EAn8CC,EAo8CDC,EAAOC,IACLyJ,GAAUxG,KAAK,CACbxL,SAAUuI,EAAQ,GAClBkK,WAAYlK,EAAQ,KAGf,IAETuB,EAASiE,IAGX,EAA8B,CAC5BtG,EAAU,CAAC,MACXG,EAAQ+E,GAAYjJ,GACpB2E,EA98CE,EA+8CFC,EAAOC,IACE,CACLmK,WAAY3D,EAAKxG,EAAQ,GAAIsC,IAC7BxJ,KAAMkH,EAAQ,KAGlBuB,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,OAAG6C,IAAKnC,EAAMmC,IAAK0I,KAAM3D,EAASjF,EAAK2I,OAAQ,IAAK,SAClDtL,SAAK6C,IAAKnC,EAAMmC,KAAMF,EAAK1I,QAMnC,EAAoB,CAClBoG,EAAU,CAAC,MAAO,OAClBG,EAAQ+E,GAAY9I,GACpBwE,EAj+CE,EAk+CFC,EAAOC,IACE,CACLqK,UAAwC,MAA7BrK,EAAQ,GAAGvG,gBAG1B8H,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,WACEyL,QAAS9I,EAAK6I,UACd3I,IAAKnC,EAAMmC,IACX6I,YACAxM,KAAK,cAMb,EAAoB,CAClBmB,EAAU,CAAC,KACXG,EAAQkF,GACN8B,EAAQQ,mBAAqBrL,EAA0BD,GAEzDuE,EAx/CE,EAy/CFC,EAAMA,CAACC,EAASC,EAAOV,KACd,CACLwE,SAAUkB,GAAYhF,EAAOD,EAAQ,GAAIT,GACzCiL,GAAIhE,EAAKxG,EAAQ,GAAIsC,IACrBmI,MAAOzK,EAAQ,GAAGY,SAGtBW,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,MACD2C,EAAKiJ,MACT,CAAED,GAAIhJ,EAAKgJ,GAAI9I,IAAKnC,EAAMmC,KAC1BD,EAAOD,EAAKuC,SAAUxE,KAK5B,GAA0B,CACxBF,EAAQkF,GAAW9I,GACnBqE,EA/gDC,EAghDDC,EAAMA,CAACC,EAASC,EAAOV,KACd,CACLwE,SAAUkB,GAAYhF,EAAOD,EAAQ,GAAIT,GACzCkL,MAAsB,MAAfzK,EAAQ,GAAa,EAAI,EAChCjC,KAAM7G,OAKZ,GAAsB,CACpBgI,EAAU,CAAC,KAIXG,EAAQmF,GAAc9I,GACtBoE,EA3hDE,EA4hDFC,EAAOC,EAASC,EAAOV,GACrB,OAASmL,GAAc1K,EAAQ,GAAGM,MAAM5C,IAElCiN,EAAc1N,WAAWyN,EAAc,MACvCE,EAAU5K,EAAQ,GAAGK,QAAQsK,EAAS,IAEtCE,GAraiBlG,EAqaeiG,EApanC9D,EAAegE,KAAKC,GAAKA,EAAE3L,KAAKuF,IAqa/BU,GACAJ,IAvaV,IAA6BN,EAyavB,MAAMqG,EAAUhL,EAAQ,GAAGvG,cACrBsQ,GAC+C,IAAnD5P,EAA6B8G,QAAQ+J,GAEjCjE,GACJgD,EAAeiB,EAAUhL,EAAQ,IACjCmD,OAEI8H,EAAM,CACVnB,MAAOhC,GAAgBf,EAAK/G,EAAQ,IACpC+J,aAAcA,EACdhD,OAWF,GAFAxH,EAAM2L,SAAW3L,EAAM2L,UAAwB,MAAZF,EAE/BjB,EACFkB,EAAInS,KAAOkH,EAAQ,OACd,CACL,MAAMmL,EAAa5L,EAAM6L,OACzB7L,EAAM6L,QAAS,EACfH,EAAIlH,SAAW8G,EAAU5K,EAAO2K,EAASrL,GACzCA,EAAM6L,OAASD,CACjB,CAQA,OAFA5L,EAAM2L,UAAW,EAEVD,CACT,EACA1J,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,EAAC2C,EAAKuF,IAAGR,GAAC7E,IAAKnC,EAAMmC,KAASF,EAAKsI,OAChCtI,EAAK1I,OAAS0I,EAAKuC,SAAWtC,EAAOD,EAAKuC,SAAUxE,GAAS,MAMtE,GAA4B,CAC1BL,EAAU,CAAC,KAIXG,EAAQmF,GAAc1I,GACtBgE,EA5lDE,EA6lDFC,EAAOC,GACL,MAAM+G,EAAM/G,EAAQ,GAAGmD,OACvB,MAAO,CACL2G,MAAOhC,GAAgBf,EAAK/G,EAAQ,IAAM,IAC1C+G,MAEJ,EACAxF,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,EAAC2C,EAAKuF,IAAGR,KAAK/E,EAAKsI,OAAOpI,IAAKnC,EAAMmC,QAIhD,GAAwB,CACtBxC,EAAU,CAAC,WACXG,EAAQmF,GAAc5I,GACtBkE,EA5mDE,EA6mDFC,EAAMA,KACG,IAETwB,EAASiE,IAGX,GAAkB,CAChBtG,EAAU,CAAC,MACXG,EAAQiF,GAAkB1C,IAC1B9B,EAtnDE,EAunDFC,EAAOC,IACE,CACLqL,IAAKtG,GAAS/E,EAAQ,IACtBmK,OAAQpF,GAAS/E,EAAQ,IACzBsL,MAAOvG,GAAS/E,EAAQ,MAG5BuB,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,SACE6C,IAAKnC,EAAMmC,IACX2J,IAAK7J,EAAK6J,UAAOlL,EACjBmL,MAAO9J,EAAK8J,YAASnL,EACrB0F,IAAKY,EAASjF,EAAK2I,OAAQ,MAAO,UAU1C,GAAiB,CACfjL,EAAU,CAAC,KACXG,EAAQ+E,GAAYzC,IACpB7B,EAzoDC,EA0oDDC,EAAMA,CAACC,EAASC,EAAOV,KACd,CACLwE,SAAUqB,GAAkBnF,EAAOD,EAAQ,GAAIT,GAC/C4K,OAAQpF,GAAS/E,EAAQ,IACzBsL,MAAOvG,GAAS/E,EAAQ,MAG5BuB,EAAOA,CAACC,EAAMC,EAAQlC,IAElBV,OACE6C,IAAKnC,EAAMmC,IACX0I,KAAM3D,EAASjF,EAAK2I,OAAQ,IAAK,QACjCmB,MAAO9J,EAAK8J,OAEX7J,EAAOD,EAAKuC,SAAUxE,KAO/B,GAAwC,CACtCL,EAAU,CAAC,KACXG,EAAQ+E,GAAYlI,GACpB4D,EA9qDC,EA+qDDC,EAAOC,IACE,CACL+D,SAAU,CACR,CACEjL,KAAMkH,EAAQ,GACdjC,KAAM7G,OAGViT,OAAQnK,EAAQ,GAChBjC,KAAM7G,QAKZ,GAAgC,CAC9BgI,EAAUA,CAACC,EAAQI,KACbA,EAAM2L,WAAY7E,EAAQkF,kBACvBvJ,GAAW7C,EAAQ,YAAc6C,GAAW7C,EAAQ,aAE7DE,EAAQ+E,GAAYpI,GACpB8D,EAnsDC,EAosDDC,EAAOC,IACE,CACL+D,SAAU,CACR,CACEjL,KAAMkH,EAAQ,GACdjC,KAAM7G,OAGViT,OAAQnK,EAAQ,GAChBsL,WAAOnL,EACPpC,KAAM7G,QAKZ,GAA+B,CAC7BgI,EAAU,CAAC,KACXG,EAAQ+E,GAAYnI,GACpB6D,EAttDC,EAutDDC,EAAOC,GACL,IAAIwL,EAAUxL,EAAQ,GAClBmK,EAASnK,EAAQ,GAOrB,OAJK1F,EAAwB8E,KAAK+K,KAChCA,EAAS,UAAYA,GAGhB,CACLpG,SAAU,CACR,CACEjL,KAAM0S,EAAQnL,QAAQ,UAAW,IACjCtC,KAAM7G,OAGViT,OAAQA,EACRpM,KAAM7G,KAEV,GAGF,GAAwB0H,GACtBC,EAp9CqB,GAw9CvB,GAA0BD,GACxBC,EAx9CuB,GA49CzB,GAA6B,CAC3BQ,EAAQkF,GAAWvJ,GACnB8E,EA7uDC,EA8uDDC,EAAQwF,GACRhE,EAAOA,IACE,MAIX,GAAsB,CACpBlC,EAAQC,GA5nBZ,SAAwBH,EAAgBI,GACtC,GACEA,EAAMK,QACNL,EAAMM,QACLN,EAAM6L,SACuB,IAA5BjM,EAAO8B,QAAQ,UACwB,IAAvC1B,EAAMG,YAAYuB,QAAQ,QAE5B,YAGF,IAAIX,EAAQ,GAEZnB,EAAOiE,MAAM,MAAMqI,MAAMC,IACvBA,GAAQ,MAGJ9E,EAA6BkE,KAAKzG,GAASA,EAAMjF,KAAKsM,MAI1DpL,GAASoL,IAEAA,EAAKvI,UAGhB,MAAMwI,EAAWtK,GAAQf,GACzB,MAAiB,KAAbqL,OAMG,CAACrL,GAASqL,EACnB,GA2lBI7L,EAtvDC,EAuvDDC,EAAQuF,GACR/D,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,OAAG6C,IAAKnC,EAAMmC,KAAMD,EAAOD,EAAKuC,SAAUxE,KAIrD,GAAgB,CACdL,EAAU,CAAC,KACXG,EAAQ+E,GAAY9H,GACpBwD,EA5wDC,EA6wDDC,EAAOC,IACL0J,GAAK1J,EAAQ,IAAM,CACjBmK,OAAQnK,EAAQ,GAChBsL,MAAOtL,EAAQ,IAGV,IAETuB,EAASiE,IAGX,GAAqB,CACnBtG,EAAU,CAAC,MACXG,EAAQiF,GAAkB/H,GAC1BuD,EA3xDC,EA4xDDC,EAAOC,IACE,CACLqL,IAAKrL,EAAQ,GAAK+E,GAAS/E,EAAQ,SAAMG,EACzC1H,IAAKuH,EAAQ,KAGjBuB,EAAOA,CAACC,EAAMC,EAAQlC,IACbmK,GAAKlI,EAAK/I,KACfoG,SACE6C,IAAKnC,EAAMmC,IACX2J,IAAK7J,EAAK6J,IACVxF,IAAKY,EAASiD,GAAKlI,EAAK/I,KAAK0R,OAAQ,MAAO,OAC5CmB,MAAO5B,GAAKlI,EAAK/I,KAAK6S,QAEtB,MAIR,GAAoB,CAClBpM,EAAWC,GAAyB,MAAdA,EAAO,KAAwC,IAA1BA,EAAO8B,QAAQ,MAC1D5B,EAAQ+E,GAAY5H,GACpBsD,EAjzDC,EAkzDDC,EAAMA,CAACC,EAASC,EAAOV,KACd,CACLwE,SAAU9D,EAAMD,EAAQ,GAAIT,GAC5BqM,iBAAkB5L,EAAQ,GAC1BvH,IAAKuH,EAAQ,KAGjBuB,EAAOA,CAACC,EAAMC,EAAQlC,IACbmK,GAAKlI,EAAK/I,KACfoG,OACE6C,IAAKnC,EAAMmC,IACX0I,KAAM3D,EAASiD,GAAKlI,EAAK/I,KAAK0R,OAAQ,IAAK,QAC3CmB,MAAO5B,GAAKlI,EAAK/I,KAAK6S,OAErB7J,EAAOD,EAAKuC,SAAUxE,IAGzBV,UAAM6C,IAAKnC,EAAMmC,KAAMF,EAAKoK,mBAKlC,GAAkB,CAChB1M,EAAU,CAAC,KACXG,EAAQkF,GAAWnI,GACnB0D,EAv0DE,EAw0DFC,EAAQ2D,GACRnC,EAAQC,EAAMC,EAAQlC,GACpB,MAAM3G,EAAQ4I,EACd,OACE3C,WAAO6C,IAAKnC,EAAMmC,KAChB7C,eACEA,YACGjG,EAAMkL,OAAOtD,IAAI,SAA4BM,EAASJ,GACrD,OACE7B,QAAI6C,IAAKhB,EAAGmL,MAAO7H,GAAcpL,EAAO8H,IACrCe,EAAOX,EAASvB,GAGvB,KAIJV,eACGjG,EAAMiK,MAAMrC,IAAI,SAA0BsL,EAAKpL,GAC9C,OACE7B,QAAI6C,IAAKhB,GACNoL,EAAItL,IAAI,SAA2BM,EAASiL,GAC3C,OACElN,QAAI6C,IAAKqK,EAAGF,MAAO7H,GAAcpL,EAAOmT,IACrCtK,EAAOX,EAASvB,GAGvB,GAGN,IAIR,GAGF,GAAiB,CAKfF,EAAQC,GAAY,SAAUH,EAAQI,GACpC,IAAIyM,EAEJ,OADIhK,GAAW7C,EAAQ,OAAM6M,EAAM3O,GAAYoC,KAAKN,IAChD6M,GAEGxO,GAAaiC,KAAKN,EAC3B,GACAW,EA72DC,EA82DDC,EAAOC,GACL,MAAMlH,EAAOkH,EAAQ,GACrB,MAAO,CACLlH,MACyB,IAAvBA,EAAKmI,QAAQ,KACTnI,EACAA,EAAKuH,QACH1E,EACA,CAACsQ,EAAMC,IAAU7F,EAAQzM,oBAAoBsS,IAAUD,GAGnE,EACA1K,EAAQC,GACCA,EAAK1I,MAIhB,GAAuB,CACrBoG,EAAU,CAAC,KAAM,MACjBG,EAAQiF,GAAkBtH,GAC1B8C,EA14DC,EA24DDC,EAAMA,CAACC,EAASC,EAAOV,KACd,CAGLwE,SAAU9D,EAAMD,EAAQ,GAAIT,KAGhCgC,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,YAAQ6C,IAAKnC,EAAMmC,KAAMD,EAAOD,EAAKuC,SAAUxE,KAI1D,GAA2B,CACzBL,EAAUC,IACR,MAAM+J,EAAO/J,EAAO,GACpB,OAAiB,MAAT+J,GAAyB,MAATA,IAAiB/J,EAAO,KAAO+J,GAEzD7J,EAAQiF,GAAkBpH,GAC1B4C,EAz5DC,EA05DDC,EAAMA,CAACC,EAASC,EAAOV,KACd,CAGLwE,SAAU9D,EAAMD,EAAQ,GAAIT,KAGhCgC,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,QAAI6C,IAAKnC,EAAMmC,KAAMD,EAAOD,EAAKuC,SAAUxE,KAItD,GAAwB,CACtBL,EAAU,CAAC,MAKXG,EAAQiF,GAAkBhH,IAC1BwC,EAr7DE,EAs7DFC,EAAOC,IACE,CACLlH,KAAMkH,EAAQ,GACdjC,KAAM7G,QAKZ,GAAuB,CACrBgI,EAAU,CAAC,MACXG,EAAQiF,GAAkBnH,GAC1B2C,EAz7DC,EA07DDC,EAAQuF,GACR/D,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,UAAM6C,IAAKnC,EAAMmC,KAAMD,EAAOD,EAAKuC,SAAUxE,KAIxD,GAAgC,CAC9BL,EAAU,CAAC,MACXG,EAAQiF,GAAkBlH,IAC1B0C,EAn8DC,EAo8DDC,EAAQuF,GACR/D,EAAOA,CAACC,EAAMC,EAAQlC,IACbV,SAAK6C,IAAKnC,EAAMmC,KAAMD,EAAOD,EAAKuC,SAAUxE,OA0CnB,IAAlC8G,EAAQ8F,+BACHxC,GAAMzS,WACNyS,GAAMzS,KAGf,MAAMuQ,GArtCR,SACEkC,GAKA,IAAIyC,EAAWC,OAAOC,KAAK3C,GAmB3B,SAAS4C,EACPpN,EACAI,GAEA,IAAI+B,EAAS,GAGb,GAFA/B,EAAMG,YAAcH,EAAMG,aAAe,GAErCP,EAAOgE,OACT,KAAOhE,GAEL,IADA,IAAIuB,EAAI,EACDA,EAAI0L,EAASxL,QAAQ,CAC1B,IAAI4L,EAAWJ,EAAS1L,GACpB+L,EAAO9C,EAAM6C,GAEjB,IAAIC,EAAKvN,GAAagD,GAAU/C,EAAQI,EAAOkN,EAAKvN,GAApD,CAKA,IAAIc,EAAUyM,EAAKpN,EAAOF,EAAQI,GAClC,GAAIS,GAAWA,EAAQ,GAAI,CACzBb,EAASA,EAAOuN,UAAU1M,EAAQ,GAAGY,QAErC,IAAI+L,EAASF,EAAK1M,EAAOC,EAASuM,EAAahN,GAE/CA,EAAMG,aAAeM,EAAQ,GAExB2M,EAAO5O,OAAM4O,EAAO5O,KAAOyO,GAChClL,EAAO2B,KAAK0J,GACZ,KACF,CACAjM,GAdA,MAFEA,GAiBJ,CAOJ,OAFAnB,EAAMG,YAAc,GAEb4B,CACT,CAEA,OA9CA8K,EAASQ,KAAK,SAAUC,EAAGC,GACzB,OAAOnD,EAAMkD,GAAG/M,EAAS6J,EAAMmD,GAAGhN,IAAW+M,EAAIC,GAAK,EAAI,EAC5D,YA4CiB3N,EAAQI,GACvB,OAAOgN,EA9FX,SAA6BpN,GAC3B,OAAOA,EACJkB,QAAQpF,EAAc,MACtBoF,QAAQjF,EAAY,IACpBiF,QAAQ3D,EAAO,OACpB,CAyFuBqQ,CAAoB5N,GAASI,EAClD,CACF,CA+oCiByN,CAAUrD,IACnBnC,IAp/BUyF,GAqClB,SACEtD,EACAuD,GAEA,gBACEjC,EACAgC,EACA1N,GAEA,MAAM4N,EAAWxD,EAAMsB,EAAIlN,MAAMwD,EAEjC,OAAO2L,EACHA,EAAW,IAAMC,EAASlC,EAAKgC,EAAQ1N,GAAQ0L,EAAKgC,EAAQ1N,GAC5D4N,EAASlC,EAAKgC,EAAQ1N,EAC5B,CACF,CAg8BqC6N,CAAezD,GAAOtD,EAAQgH,qBAn/BjDC,EACdrC,EACA1L,EAA6B,IAE7B,GAAI6C,MAAMC,QAAQ4I,GAAM,CACtB,MAAMsC,EAAShO,EAAMmC,IACfJ,EAAS,GAIf,IAAIkM,GAAgB,EAEpB,IAAK,IAAI9M,EAAI,EAAGA,EAAIuK,EAAIrK,OAAQF,IAAK,CACnCnB,EAAMmC,IAAMhB,EAEZ,MAAM+M,EAAUH,EAAcrC,EAAIvK,GAAInB,GAChCmO,EAA8B,iBAAZD,EAEpBC,GAAYF,EACdlM,EAAOA,EAAOV,OAAS,IAAM6M,EACR,OAAZA,GACTnM,EAAO2B,KAAKwK,GAGdD,EAAgBE,CAClB,CAIA,OAFAnO,EAAMmC,IAAM6L,EAELjM,CACT,CAEA,OAAO2L,GAAOhC,EAAKqC,EAAe/N,EACpC,GAlCF,IAAkB0N,GAs/BhB,MAAMrF,GAAMP,GAAQjB,GAEpB,OAAIqD,GAAU7I,OAEV/B,aACG+I,GACD/I,YAAQ6C,IAAI,UACT+H,GAAUjJ,IAAI,SAAwBmN,GACrC,OACE9O,SAAK2L,GAAIhE,EAAKmH,EAAIzD,WAAY5H,IAAUZ,IAAKiM,EAAIzD,YAC9CyD,EAAIzD,WACJ1C,GAAQC,GAAOkG,EAAIlW,SAAU,CAAEmI,QAAQ,KAG9C,KAMDgI,EACT,gBAWIgG,QAAC7J,SAAEA,EAAW,GAAEsC,QAAEA,GAAmBuH,EAAP5G,oIAAK6G,CAAAD,EAAAE,GAQrC,OAAOnH,EAAMoH,aACX5H,GAASpC,EAAUsC,GACnBW,EAAsC"}