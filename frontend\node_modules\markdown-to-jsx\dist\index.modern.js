import*as e from"react";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},n.apply(this,arguments)}const r=["children","options"],t={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},o=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,n)=>(e[n.toLowerCase()]=n,e),{class:"className",for:"htmlFor"}),a={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},c=["style","script","pre"],i=["src","href","data","formAction","srcDoc","action"],u=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,l=/mailto:/i,s=/\n{2,}$/,f=/^(\s*>[\s\S]*?)(?=\n\n|$)/,_=/^ *> ?/gm,d=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,p=/^ {2,}\n/,y=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,h=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,g=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,m=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,k=/^(?:\n *)*\n/,x=/\r\n?/g,v=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,q=/^\[\^([^\]]+)]/,b=/\f/g,S=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,$=/^\s*?\[(x|\s)\]/,z=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,E=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^([^\n]+)\n *(=|-){3,} *\n/,R=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,B=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,L=/^<!--[\s\S]*?(?:-->)/,O=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,j=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,C=/^\{.*\}$/,I=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,T=/^<([^ >]+@[^ >]+)>/,M=/^<([^ >]+:\/[^ >]+)>/,w=/-([a-z])?/gi,D=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,F=/^[^\n]+(?:  \n|\n{2,})/,P=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,Z=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,N=/^\[([^\]]*)\] ?\[([^\]]*)\]/,G=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,U=/\t/g,V=/(^ *\||\| *$)/g,H=/^ *:-+: *$/,Q=/^ *:-+ *$/,W=/^ *-+: *$/,J="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",K=RegExp(`^([*_])\\1${J}\\1\\1(?!\\1)`),X=RegExp(`^([*_])${J}\\1(?!\\1)`),Y=RegExp(`^(==)${J}\\1`),ee=RegExp(`^(~~)${J}\\1`),ne=/^(:[a-zA-Z0-9-_]+:)/,re=/^\\([^0-9A-Za-z\s])/,te=/\\([^0-9A-Za-z\s])/g,oe=/^[\s\S](?:(?!  \n|[0-9]\.|http)[^=*_~\-\n:<`\\\[!])*/,ae=/^\n+/,ce=/^([ \t]*)/,ie=/(?:^|\n)( *)$/,ue="(?:\\d+\\.)",le="(?:[*+-])";function se(e){return"( *)("+(1===e?ue:le)+") +"}const fe=se(1),_e=se(2);function de(e){return RegExp("^"+(1===e?fe:_e))}const pe=de(1),ye=de(2);function he(e){return RegExp("^"+(1===e?fe:_e)+"[^\\n]*(?:\\n(?!\\1"+(1===e?ue:le)+" )[^\\n]*)*(\\n|$)","gm")}const ge=he(1),me=he(2);function ke(e){const n=1===e?ue:le;return RegExp("^( *)("+n+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+n+" (?!"+n+" ))\\n*|\\s*\\n*$)")}const xe=ke(1),ve=ke(2);function qe(e,n){const r=1===n,t=r?xe:ve,o=r?ge:me,a=r?pe:ye;return{t:e=>a.test(e),o:je(function(e,n){const r=ie.exec(n.prevCapture);return r&&(n.list||!n.inline&&!n.simple)?t.exec(e=r[1]+e):null}),i:1,u(e,n,t){const c=r?+e[2]:void 0,i=e[0].replace(s,"\n").match(o);let u=!1;return{items:i.map(function(e,r){const o=a.exec(e)[0].length,c=RegExp("^ {1,"+o+"}","gm"),l=e.replace(c,"").replace(a,""),s=r===i.length-1,f=-1!==l.indexOf("\n\n")||s&&u;u=f;const _=t.inline,d=t.list;let p;t.list=!0,f?(t.inline=!1,p=$e(l)+"\n\n"):(t.inline=!0,p=$e(l));const y=n(p,t);return t.inline=_,t.list=d,y}),ordered:r,start:c}},l:(n,r,t)=>e(n.ordered?"ol":"ul",{key:t.key,start:"20"===n.type?n.start:void 0},n.items.map(function(n,o){return e("li",{key:o},r(n,t))}))}}const be=RegExp("^\\[((?:\\[[^\\[\\]]*(?:\\[[^\\[\\]]*\\][^\\[\\]]*)*\\]|[^\\[\\]])*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Se=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;function $e(e){let n=e.length;for(;n>0&&e[n-1]<=" ";)n--;return e.slice(0,n)}function ze(e,n){return e.startsWith(n)}function Ee(e,n,r){if(Array.isArray(r)){for(let n=0;n<r.length;n++)if(ze(e,r[n]))return!0;return!1}return r(e,n)}function Ae(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Re(e){return W.test(e)?"right":H.test(e)?"center":Q.test(e)?"left":null}function Be(e,n,r,t){const o=r.inTable;r.inTable=!0;let a=[[]],c="";function i(){if(!c)return;const e=a[a.length-1];e.push.apply(e,n(c,r)),c=""}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e,n,r)=>{"|"===e.trim()&&(i(),t)?0!==n&&n!==r.length-1&&a.push([]):c+=e}),i(),r.inTable=o,a}function Le(e,n,r){r.inline=!0;const t=e[2]?e[2].replace(V,"").split("|").map(Re):[],o=e[3]?function(e,n,r){return e.trim().split("\n").map(function(e){return Be(e,n,r,!0)})}(e[3],n,r):[],a=Be(e[1],n,r,!!o.length);return r.inline=!1,o.length?{align:t,cells:o,header:a,type:"25"}:{children:a,type:"21"}}function Oe(e,n){return null==e.align[n]?{}:{textAlign:e.align[n]}}function je(e){return e.inline=1,e}function Ce(e){return je(function(n,r){return r.inline?e.exec(n):null})}function Ie(e){return je(function(n,r){return r.inline||r.simple?e.exec(n):null})}function Te(e){return function(n,r){return r.inline||r.simple?null:e.exec(n)}}function Me(e){return je(function(n){return e.exec(n)})}const we=/(javascript|vbscript|data(?!:image)):/i;function De(e){try{const n=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(we.test(n))return null}catch(e){return null}return e}function Fe(e){return e?e.replace(te,"$1"):e}function Pe(e,n,r){const t=r.inline||!1,o=r.simple||!1;r.inline=!0,r.simple=!0;const a=e(n,r);return r.inline=t,r.simple=o,a}function Ze(e,n,r){const t=r.inline||!1,o=r.simple||!1;r.inline=!1,r.simple=!0;const a=e(n,r);return r.inline=t,r.simple=o,a}function Ne(e,n,r){const t=r.inline||!1;r.inline=!1;const o=e(n,r);return r.inline=t,o}const Ge=(e,n,r)=>({children:Pe(n,e[2],r)});function Ue(){return{}}function Ve(){return null}function He(...e){return e.filter(Boolean).join(" ")}function Qe(e,n,r){let t=e;const o=n.split(".");for(;o.length&&(t=t[o[0]],void 0!==t);)o.shift();return t||r}function We(r="",t={}){t.overrides=t.overrides||{},t.namedCodesToUnicode=t.namedCodesToUnicode?n({},a,t.namedCodesToUnicode):a;const s=t.slugify||Ae,V=t.sanitizer||De,H=t.createElement||e.createElement,Q=[f,h,g,t.enforceAtxHeadings?E:z,A,D,xe,ve],W=[...Q,F,R,L,j];function J(e,r,...o){const a=Qe(t.overrides,e+".props",{});return H(function(e,n){const r=Qe(n,e);return r?"function"==typeof r||"object"==typeof r&&"render"in r?r:Qe(n,e+".component",e):e}(e,t.overrides),n({},r,a,{className:He(null==r?void 0:r.className,a.className)||void 0}),...o)}function te(e){e=e.replace(S,"");let n=!1;t.forceInline?n=!0:t.forceBlock||(n=!1===G.test(e));const r=_e(fe(n?e:$e(e).replace(ae,"")+"\n\n",{inline:n}));for(;"string"==typeof r[r.length-1]&&!r[r.length-1].trim();)r.pop();if(null===t.wrapper)return r;const o=t.wrapper||(n?"span":"div");let a;if(r.length>1||t.forceWrapper)a=r;else{if(1===r.length)return a=r[0],"string"==typeof a?J("span",{key:"outer"},a):a;a=null}return H(o,{key:"outer"},a)}function ie(e,n){if(!n||!n.trim())return null;const r=n.match(u);return r?r.reduce(function(n,r){const t=r.indexOf("=");if(-1!==t){const a=function(e){return-1!==e.indexOf("-")&&null===e.match(O)&&(e=e.replace(w,function(e,n){return n.toUpperCase()})),e}(r.slice(0,t)).trim(),c=function(e){const n=e[0];return('"'===n||"'"===n)&&e.length>=2&&e[e.length-1]===n?e.slice(1,-1):e}(r.slice(t+1).trim()),u=o[a]||a;if("ref"===u)return n;const l=n[u]=function(e,n,r,t){return"style"===n?function(e){const n=[];let r="",t=!1,o=!1,a="";if(!e)return n;for(let c=0;c<e.length;c++){const i=e[c];if('"'!==i&&"'"!==i||t||(o?i===a&&(o=!1,a=""):(o=!0,a=i)),"("===i&&r.endsWith("url")?t=!0:")"===i&&t&&(t=!1),";"!==i||o||t)r+=i;else{const e=r.trim();if(e){const r=e.indexOf(":");if(r>0){const t=e.slice(0,r).trim(),o=e.slice(r+1).trim();n.push([t,o])}}r=""}}const c=r.trim();if(c){const e=c.indexOf(":");if(e>0){const r=c.slice(0,e).trim(),t=c.slice(e+1).trim();n.push([r,t])}}return n}(r).reduce(function(n,[r,o]){return n[r.replace(/(-[a-z])/g,e=>e[1].toUpperCase())]=t(o,e,r),n},{}):-1!==i.indexOf(n)?t(Fe(r),e,n):(r.match(C)&&(r=Fe(r.slice(1,r.length-1))),"true"===r||"false"!==r&&r)}(e,a,c,V);"string"==typeof l&&(R.test(l)||j.test(l))&&(n[u]=te(l.trim()))}else"style"!==r&&(n[o[r]||r]=!0);return n},{}):null}const ue=[],le={},se={0:{t:[">"],o:Te(f),i:1,u(e,n,r){const[,t,o]=e[0].replace(_,"").match(d);return{alert:t,children:n(o,r)}},l(e,n,r){const t={key:r.key};return e.alert&&(t.className="markdown-alert-"+s(e.alert.toLowerCase(),Ae),e.children.unshift({attrs:{},children:[{type:"27",text:e.alert}],noInnerParse:!0,type:"11",tag:"header"})),J("blockquote",t,n(e.children,r))}},1:{o:Me(p),i:1,u:Ue,l:(e,n,r)=>J("br",{key:r.key})},2:{t:e=>{const n=e[0];return"-"===n||"*"===n||"_"===n},o:Te(y),i:1,u:Ue,l:(e,n,r)=>J("hr",{key:r.key})},3:{t:["    "],o:Te(g),i:0,u:e=>({lang:void 0,text:Fe($e(e[0].replace(/^ {4}/gm,"")))}),l:(e,r,t)=>J("pre",{key:t.key},J("code",n({},e.attrs,{className:e.lang?"lang-"+e.lang:""}),e.text))},4:{t:["```","~~~"],o:Te(h),i:0,u:e=>({attrs:ie("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:"3"})},5:{t:["`"],o:Ie(m),i:3,u:e=>({text:Fe(e[2])}),l:(e,n,r)=>J("code",{key:r.key},e.text)},6:{t:["[^"],o:Te(v),i:0,u:e=>(ue.push({footnote:e[2],identifier:e[1]}),{}),l:Ve},7:{t:["[^"],o:Ce(q),i:1,u:e=>({target:"#"+s(e[1],Ae),text:e[1]}),l:(e,n,r)=>J("a",{key:r.key,href:V(e.target,"a","href")},J("sup",{key:r.key},e.text))},8:{t:["[ ]","[x]"],o:Ce($),i:1,u:e=>({completed:"x"===e[1].toLowerCase()}),l:(e,n,r)=>J("input",{checked:e.completed,key:r.key,readOnly:!0,type:"checkbox"})},9:{t:["#"],o:Te(t.enforceAtxHeadings?E:z),i:1,u:(e,n,r)=>({children:Pe(n,e[2],r),id:s(e[2],Ae),level:e[1].length}),l:(e,n,r)=>J("h"+e.level,{id:e.id,key:r.key},n(e.children,r))},10:{o:Te(A),i:0,u:(e,n,r)=>({children:Pe(n,e[1],r),level:"="===e[2]?1:2,type:"9"})},11:{t:["<"],o:Me(R),i:1,u(e,n,r){const[,t]=e[3].match(ce),o=RegExp("^"+t,"gm"),a=e[3].replace(o,""),i=(u=a,W.some(e=>e.test(u))?Ne:Pe);var u;const l=e[1].toLowerCase(),s=-1!==c.indexOf(l),f=(s?l:e[1]).trim(),_={attrs:ie(f,e[2]),noInnerParse:s,tag:f};if(r.inAnchor=r.inAnchor||"a"===l,s)_.text=e[3];else{const e=r.inHTML;r.inHTML=!0,_.children=i(n,a,r),r.inHTML=e}return r.inAnchor=!1,_},l:(e,r,t)=>J(e.tag,n({key:t.key},e.attrs),e.text||(e.children?r(e.children,t):""))},13:{t:["<"],o:Me(j),i:1,u(e){const n=e[1].trim();return{attrs:ie(n,e[2]||""),tag:n}},l:(e,r,t)=>J(e.tag,n({},e.attrs,{key:t.key}))},12:{t:["\x3c!--"],o:Me(L),i:1,u:()=>({}),l:Ve},14:{t:["!["],o:Ie(Se),i:1,u:e=>({alt:Fe(e[1]),target:Fe(e[2]),title:Fe(e[3])}),l:(e,n,r)=>J("img",{key:r.key,alt:e.alt||void 0,title:e.title||void 0,src:V(e.target,"img","src")})},15:{t:["["],o:Ce(be),i:3,u:(e,n,r)=>({children:Ze(n,e[1],r),target:Fe(e[2]),title:Fe(e[3])}),l:(e,n,r)=>J("a",{key:r.key,href:V(e.target,"a","href"),title:e.title},n(e.children,r))},16:{t:["<"],o:Ce(M),i:0,u:e=>({children:[{text:e[1],type:"27"}],target:e[1],type:"15"})},17:{t:(e,n)=>!n.inAnchor&&!t.disableAutoLink&&(ze(e,"http://")||ze(e,"https://")),o:Ce(I),i:0,u:e=>({children:[{text:e[1],type:"27"}],target:e[1],title:void 0,type:"15"})},18:{t:["<"],o:Ce(T),i:0,u(e){let n=e[1],r=e[1];return l.test(r)||(r="mailto:"+r),{children:[{text:n.replace("mailto:",""),type:"27"}],target:r,type:"15"}}},20:qe(J,1),33:qe(J,2),19:{o:Te(k),i:3,u:Ue,l:()=>"\n"},21:{o:je(function(e,n){if(n.inline||n.simple||n.inHTML&&-1===e.indexOf("\n\n")&&-1===n.prevCapture.indexOf("\n\n"))return null;let r="";e.split("\n").every(e=>(e+="\n",!Q.some(n=>n.test(e))&&(r+=e,!!e.trim())));const t=$e(r);return""===t?null:[r,,t]}),i:3,u:Ge,l:(e,n,r)=>J("p",{key:r.key},n(e.children,r))},22:{t:["["],o:Ce(P),i:0,u:e=>(le[e[1]]={target:e[2],title:e[4]},{}),l:Ve},23:{t:["!["],o:Ie(Z),i:0,u:e=>({alt:e[1]?Fe(e[1]):void 0,ref:e[2]}),l:(e,n,r)=>le[e.ref]?J("img",{key:r.key,alt:e.alt,src:V(le[e.ref].target,"img","src"),title:le[e.ref].title}):null},24:{t:e=>"["===e[0]&&-1===e.indexOf("]("),o:Ce(N),i:0,u:(e,n,r)=>({children:n(e[1],r),fallbackChildren:e[0],ref:e[2]}),l:(e,n,r)=>le[e.ref]?J("a",{key:r.key,href:V(le[e.ref].target,"a","href"),title:le[e.ref].title},n(e.children,r)):J("span",{key:r.key},e.fallbackChildren)},25:{t:["|"],o:Te(D),i:1,u:Le,l(e,n,r){const t=e;return J("table",{key:r.key},J("thead",null,J("tr",null,t.header.map(function(e,o){return J("th",{key:o,style:Oe(t,o)},n(e,r))}))),J("tbody",null,t.cells.map(function(e,o){return J("tr",{key:o},e.map(function(e,o){return J("td",{key:o,style:Oe(t,o)},n(e,r))}))})))}},27:{o:je(function(e,n){let r;return ze(e,":")&&(r=ne.exec(e)),r||oe.exec(e)}),i:4,u(e){const n=e[0];return{text:-1===n.indexOf("&")?n:n.replace(B,(e,n)=>t.namedCodesToUnicode[n]||e)}},l:e=>e.text},28:{t:["**","__"],o:Ie(K),i:2,u:(e,n,r)=>({children:n(e[2],r)}),l:(e,n,r)=>J("strong",{key:r.key},n(e.children,r))},29:{t:e=>{const n=e[0];return("*"===n||"_"===n)&&e[1]!==n},o:Ie(X),i:3,u:(e,n,r)=>({children:n(e[2],r)}),l:(e,n,r)=>J("em",{key:r.key},n(e.children,r))},30:{t:["\\"],o:Ie(re),i:1,u:e=>({text:e[1],type:"27"})},31:{t:["=="],o:Ie(Y),i:3,u:Ge,l:(e,n,r)=>J("mark",{key:r.key},n(e.children,r))},32:{t:["~~"],o:Ie(ee),i:3,u:Ge,l:(e,n,r)=>J("del",{key:r.key},n(e.children,r))}};!0===t.disableParsingRawHTML&&(delete se[11],delete se[13]);const fe=function(e){var n=Object.keys(e);function r(t,o){var a=[];if(o.prevCapture=o.prevCapture||"",t.trim())for(;t;)for(var c=0;c<n.length;){var i=n[c],u=e[i];if(!u.t||Ee(t,o,u.t)){var l=u.o(t,o);if(l&&l[0]){t=t.substring(l[0].length);var s=u.u(l,r,o);o.prevCapture+=l[0],s.type||(s.type=i),a.push(s);break}c++}else c++}return o.prevCapture="",a}return n.sort(function(n,r){return e[n].i-e[r].i||(n<r?-1:1)}),function(e,n){return r(function(e){return e.replace(x,"\n").replace(b,"").replace(U,"    ")}(e),n)}}(se),_e=(de=function(e,n){return function(r,t,o){const a=e[r.type].l;return n?n(()=>a(r,t,o),r,t,o):a(r,t,o)}}(se,t.renderRule),function e(n,r={}){if(Array.isArray(n)){const t=r.key,o=[];let a=!1;for(let t=0;t<n.length;t++){r.key=t;const c=e(n[t],r),i="string"==typeof c;i&&a?o[o.length-1]+=c:null!==c&&o.push(c),a=i}return r.key=t,o}return de(n,e,r)});var de;const pe=te(r);return ue.length?J("div",null,pe,J("footer",{key:"footer"},ue.map(function(e){return J("div",{id:s(e.identifier,Ae),key:e.identifier},e.identifier,_e(fe(e.footnote,{inline:!0})))}))):pe}export default n=>{let{children:t="",options:o}=n,a=function(e,n){if(null==e)return{};var r,t,o={},a=Object.keys(e);for(t=0;t<a.length;t++)n.indexOf(r=a[t])>=0||(o[r]=e[r]);return o}(n,r);return e.cloneElement(We(t,o),a)};export{t as RuleType,We as compiler,De as sanitizer,Ae as slugify};
//# sourceMappingURL=index.modern.js.map
