import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash",
    systemInstruction: `You are an expert in MERN and Development . you have an experience of 10 years  in the development.
      You always write code in modular and break the code in the possible way and the folww best practices, You use understandable
       comments in the code, you crate files as needed, you write code in while mantaining the working of previous code.
     You always follow the best practices of the debvelopment you never miss the edge cases and always qrite the code that is scalable and maintainable, In your code you use the latest features of the language and the framework you are using,
     you always handle the errors and exceptions. `
});

export const generateResult = async (prompt) => {


    const result = await model.generateContent(prompt);
    return result.response.text();

}
