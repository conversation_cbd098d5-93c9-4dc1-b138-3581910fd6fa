!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("react")):"function"==typeof define&&define.amd?define(["react"],n):(r||self).MarkdownToJSX=n(r.<PERSON>act)}(this,function(r){function n(r){if(r&&r.t)return r;var n=Object.create(null);return r&&Object.keys(r).forEach(function(e){if("default"!==e){var t=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(n,e,t.get?t:{enumerable:!0,get:function(){return r[e]}})}}),n.default=r,n}var e=/*#__PURE__*/n(r);function t(){return t=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},t.apply(this,arguments)}var u=["children","options"],a={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},i=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(r,n){return r[n.toLowerCase()]=n,r},{class:"className",for:"htmlFor"}),o={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},c=["style","script","pre"],f=["src","href","data","formAction","srcDoc","action"],l=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,d=/mailto:/i,_=/\n{2,}$/,s=/^(\s*>[\s\S]*?)(?=\n\n|$)/,p=/^ *> ?/gm,v=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,y=/^ {2,}\n/,h=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,g=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,m=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,k=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,x=/^(?:\n *)*\n/,b=/\r\n?/g,q=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,S=/^\[\^([^\]]+)]/,z=/\f/g,$=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,E=/^\s*?\[(x|\s)\]/,j=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,O=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,R=/^([^\n]+)\n *(=|-){3,} *\n/,A=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,T=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,B=/^<!--[\s\S]*?(?:-->)/,L=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,C=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,I=/^\{.*\}$/,M=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,w=/^<([^ >]+@[^ >]+)>/,D=/^<([^ >]+:\/[^ >]+)>/,F=/-([a-z])?/gi,P=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,Z=/^[^\n]+(?:  \n|\n{2,})/,N=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,G=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,U=/^\[([^\]]*)\] ?\[([^\]]*)\]/,V=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,H=/\t/g,Q=/(^ *\||\| *$)/g,W=/^ *:-+: *$/,J=/^ *:-+ *$/,K=/^ *-+: *$/,X="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",Y=RegExp("^([*_])\\1"+X+"\\1\\1(?!\\1)"),rr=RegExp("^([*_])"+X+"\\1(?!\\1)"),nr=RegExp("^(==)"+X+"\\1"),er=RegExp("^(~~)"+X+"\\1"),tr=/^(:[a-zA-Z0-9-_]+:)/,ur=/^\\([^0-9A-Za-z\s])/,ar=/\\([^0-9A-Za-z\s])/g,ir=/^[\s\S](?:(?!  \n|[0-9]\.|http)[^=*_~\-\n:<`\\\[!])*/,or=/^\n+/,cr=/^([ \t]*)/,fr=/(?:^|\n)( *)$/,lr="(?:\\d+\\.)",dr="(?:[*+-])";function _r(r){return"( *)("+(1===r?lr:dr)+") +"}var sr=_r(1),pr=_r(2);function vr(r){return RegExp("^"+(1===r?sr:pr))}var yr=vr(1),hr=vr(2);function gr(r){return RegExp("^"+(1===r?sr:pr)+"[^\\n]*(?:\\n(?!\\1"+(1===r?lr:dr)+" )[^\\n]*)*(\\n|$)","gm")}var mr=gr(1),kr=gr(2);function xr(r){var n=1===r?lr:dr;return RegExp("^( *)("+n+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+n+" (?!"+n+" ))\\n*|\\s*\\n*$)")}var br=xr(1),qr=xr(2);function Sr(r,n){var e=1===n,t=e?br:qr,u=e?mr:kr,i=e?yr:hr;return{u:function(r){return i.test(r)},i:Cr(function(r,n){var e=fr.exec(n.prevCapture);return e&&(n.list||!n.inline&&!n.simple)?t.exec(r=e[1]+r):null}),o:1,l:function(r,n,t){var a=e?+r[2]:void 0,o=r[0].replace(_,"\n").match(u),c=!1;return{items:o.map(function(r,e){var u=i.exec(r)[0].length,a=RegExp("^ {1,"+u+"}","gm"),f=r.replace(a,"").replace(i,""),l=e===o.length-1,d=-1!==f.indexOf("\n\n")||l&&c;c=d;var _,s=t.inline,p=t.list;t.list=!0,d?(t.inline=!1,_=Er(f)+"\n\n"):(t.inline=!0,_=Er(f));var v=n(_,t);return t.inline=s,t.list=p,v}),ordered:e,start:a}},_:function(n,e,t){return r(n.ordered?"ol":"ul",{key:t.key,start:n.type===a.orderedList?n.start:void 0},n.items.map(function(n,u){return r("li",{key:u},e(n,t))}))}}}var zr=RegExp("^\\[((?:\\[[^\\[\\]]*(?:\\[[^\\[\\]]*\\][^\\[\\]]*)*\\]|[^\\[\\]])*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),$r=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;function Er(r){for(var n=r.length;n>0&&r[n-1]<=" ";)n--;return r.slice(0,n)}function jr(r,n){return r.startsWith(n)}function Or(r,n,e){if(Array.isArray(e)){for(var t=0;t<e.length;t++)if(jr(r,e[t]))return!0;return!1}return e(r,n)}function Rr(r){return r.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Ar(r){return K.test(r)?"right":W.test(r)?"center":J.test(r)?"left":null}function Tr(r,n,e,t){var u=e.inTable;e.inTable=!0;var a=[[]],i="";function o(){if(i){var r=a[a.length-1];r.push.apply(r,n(i,e)),i=""}}return r.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(r,n,e){"|"===r.trim()&&(o(),t)?0!==n&&n!==e.length-1&&a.push([]):i+=r}),o(),e.inTable=u,a}function Br(r,n,e){e.inline=!0;var t=r[2]?r[2].replace(Q,"").split("|").map(Ar):[],u=r[3]?function(r,n,e){return r.trim().split("\n").map(function(r){return Tr(r,n,e,!0)})}(r[3],n,e):[],i=Tr(r[1],n,e,!!u.length);return e.inline=!1,u.length?{align:t,cells:u,header:i,type:a.table}:{children:i,type:a.paragraph}}function Lr(r,n){return null==r.align[n]?{}:{textAlign:r.align[n]}}function Cr(r){return r.inline=1,r}function Ir(r){return Cr(function(n,e){return e.inline?r.exec(n):null})}function Mr(r){return Cr(function(n,e){return e.inline||e.simple?r.exec(n):null})}function wr(r){return function(n,e){return e.inline||e.simple?null:r.exec(n)}}function Dr(r){return Cr(function(n){return r.exec(n)})}var Fr=/(javascript|vbscript|data(?!:image)):/i;function Pr(r){try{var n=decodeURIComponent(r).replace(/[^A-Za-z0-9/:]/g,"");if(Fr.test(n))return null}catch(r){return null}return r}function Zr(r){return r?r.replace(ar,"$1"):r}function Nr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!0,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Gr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!1,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Ur(r,n,e){var t=e.inline||!1;e.inline=!1;var u=r(n,e);return e.inline=t,u}var Vr=function(r,n,e){return{children:Nr(n,r[2],e)}};function Hr(){return{}}function Qr(){return null}function Wr(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Jr(r,n,e){for(var t=r,u=n.split(".");u.length&&void 0!==(t=t[u[0]]);)u.shift();return t||e}function Kr(r,n){var e=Jr(n,r);return e?"function"==typeof e||"object"==typeof e&&"render"in e?e:Jr(n,r+".component",r):r}function Xr(r,n){var u;void 0===r&&(r=""),void 0===n&&(n={}),n.overrides=n.overrides||{},n.namedCodesToUnicode=n.namedCodesToUnicode?t({},o,n.namedCodesToUnicode):o;var _=n.slugify||Rr,Q=n.sanitizer||Pr,W=n.createElement||e.createElement,J=[s,g,m,n.enforceAtxHeadings?O:j,R,P,br,qr],K=[].concat(J,[Z,A,B,C]);function X(r,e){var u=Jr(n.overrides,r+".props",{});return W.apply(void 0,[Kr(r,n.overrides),t({},e,u,{className:Wr(null==e?void 0:e.className,u.className)||void 0})].concat([].slice.call(arguments,2)))}function ar(r){r=r.replace($,"");var e=!1;n.forceInline?e=!0:n.forceBlock||(e=!1===V.test(r));for(var t=vr(pr(e?r:Er(r).replace(or,"")+"\n\n",{inline:e}));"string"==typeof t[t.length-1]&&!t[t.length-1].trim();)t.pop();if(null===n.wrapper)return t;var u,a=n.wrapper||(e?"span":"div");if(t.length>1||n.forceWrapper)u=t;else{if(1===t.length)return"string"==typeof(u=t[0])?X("span",{key:"outer"},u):u;u=null}return W(a,{key:"outer"},u)}function fr(r,n){if(!n||!n.trim())return null;var e=n.match(l);return e?e.reduce(function(n,e){var t=e.indexOf("=");if(-1!==t){var u=function(r){return-1!==r.indexOf("-")&&null===r.match(L)&&(r=r.replace(F,function(r,n){return n.toUpperCase()})),r}(e.slice(0,t)).trim(),a=function(r){var n=r[0];return('"'===n||"'"===n)&&r.length>=2&&r[r.length-1]===n?r.slice(1,-1):r}(e.slice(t+1).trim()),o=i[u]||u;if("ref"===o)return n;var c=n[o]=function(r,n,e,t){return"style"===n?function(r){var n=[],e="",t=!1,u=!1,a="";if(!r)return n;for(var i=0;i<r.length;i++){var o=r[i];if('"'!==o&&"'"!==o||t||(u?o===a&&(u=!1,a=""):(u=!0,a=o)),"("===o&&e.endsWith("url")?t=!0:")"===o&&t&&(t=!1),";"!==o||u||t)e+=o;else{var c=e.trim();if(c){var f=c.indexOf(":");if(f>0){var l=c.slice(0,f).trim(),d=c.slice(f+1).trim();n.push([l,d])}}e=""}}var _=e.trim();if(_){var s=_.indexOf(":");if(s>0){var p=_.slice(0,s).trim(),v=_.slice(s+1).trim();n.push([p,v])}}return n}(e).reduce(function(n,e){var u=e[0],a=e[1];return n[u.replace(/(-[a-z])/g,function(r){return r[1].toUpperCase()})]=t(a,r,u),n},{}):-1!==f.indexOf(n)?t(Zr(e),r,n):(e.match(I)&&(e=Zr(e.slice(1,e.length-1))),"true"===e||"false"!==e&&e)}(r,u,a,Q);"string"==typeof c&&(A.test(c)||C.test(c))&&(n[o]=ar(c.trim()))}else"style"!==e&&(n[i[e]||e]=!0);return n},{}):null}var lr=[],dr={},_r=((u={})[a.blockQuote]={u:[">"],i:wr(s),o:1,l:function(r,n,e){var t=r[0].replace(p,"").match(v);return{alert:t[1],children:n(t[2],e)}},_:function(r,n,e){var t={key:e.key};return r.alert&&(t.className="markdown-alert-"+_(r.alert.toLowerCase(),Rr),r.children.unshift({attrs:{},children:[{type:a.text,text:r.alert}],noInnerParse:!0,type:a.htmlBlock,tag:"header"})),X("blockquote",t,n(r.children,e))}},u[a.breakLine]={i:Dr(y),o:1,l:Hr,_:function(r,n,e){return X("br",{key:e.key})}},u[a.breakThematic]={u:function(r){var n=r[0];return"-"===n||"*"===n||"_"===n},i:wr(h),o:1,l:Hr,_:function(r,n,e){return X("hr",{key:e.key})}},u[a.codeBlock]={u:["    "],i:wr(m),o:0,l:function(r){return{lang:void 0,text:Zr(Er(r[0].replace(/^ {4}/gm,"")))}},_:function(r,n,e){return X("pre",{key:e.key},X("code",t({},r.attrs,{className:r.lang?"lang-"+r.lang:""}),r.text))}},u[a.codeFenced]={u:["```","~~~"],i:wr(g),o:0,l:function(r){return{attrs:fr("code",r[3]||""),lang:r[2]||void 0,text:r[4],type:a.codeBlock}}},u[a.codeInline]={u:["`"],i:Mr(k),o:3,l:function(r){return{text:Zr(r[2])}},_:function(r,n,e){return X("code",{key:e.key},r.text)}},u[a.footnote]={u:["[^"],i:wr(q),o:0,l:function(r){return lr.push({footnote:r[2],identifier:r[1]}),{}},_:Qr},u[a.footnoteReference]={u:["[^"],i:Ir(S),o:1,l:function(r){return{target:"#"+_(r[1],Rr),text:r[1]}},_:function(r,n,e){return X("a",{key:e.key,href:Q(r.target,"a","href")},X("sup",{key:e.key},r.text))}},u[a.gfmTask]={u:["[ ]","[x]"],i:Ir(E),o:1,l:function(r){return{completed:"x"===r[1].toLowerCase()}},_:function(r,n,e){return X("input",{checked:r.completed,key:e.key,readOnly:!0,type:"checkbox"})}},u[a.heading]={u:["#"],i:wr(n.enforceAtxHeadings?O:j),o:1,l:function(r,n,e){return{children:Nr(n,r[2],e),id:_(r[2],Rr),level:r[1].length}},_:function(r,n,e){return X("h"+r.level,{id:r.id,key:e.key},n(r.children,e))}},u[a.headingSetext]={i:wr(R),o:0,l:function(r,n,e){return{children:Nr(n,r[1],e),level:"="===r[2]?1:2,type:a.heading}}},u[a.htmlBlock]={u:["<"],i:Dr(A),o:1,l:function(r,n,e){var t,u=r[3].match(cr),a=RegExp("^"+u[1],"gm"),i=r[3].replace(a,""),o=(t=i,K.some(function(r){return r.test(t)})?Ur:Nr),f=r[1].toLowerCase(),l=-1!==c.indexOf(f),d=(l?f:r[1]).trim(),_={attrs:fr(d,r[2]),noInnerParse:l,tag:d};if(e.inAnchor=e.inAnchor||"a"===f,l)_.text=r[3];else{var s=e.inHTML;e.inHTML=!0,_.children=o(n,i,e),e.inHTML=s}return e.inAnchor=!1,_},_:function(r,n,e){return X(r.tag,t({key:e.key},r.attrs),r.text||(r.children?n(r.children,e):""))}},u[a.htmlSelfClosing]={u:["<"],i:Dr(C),o:1,l:function(r){var n=r[1].trim();return{attrs:fr(n,r[2]||""),tag:n}},_:function(r,n,e){return X(r.tag,t({},r.attrs,{key:e.key}))}},u[a.htmlComment]={u:["\x3c!--"],i:Dr(B),o:1,l:function(){return{}},_:Qr},u[a.image]={u:["!["],i:Mr($r),o:1,l:function(r){return{alt:Zr(r[1]),target:Zr(r[2]),title:Zr(r[3])}},_:function(r,n,e){return X("img",{key:e.key,alt:r.alt||void 0,title:r.title||void 0,src:Q(r.target,"img","src")})}},u[a.link]={u:["["],i:Ir(zr),o:3,l:function(r,n,e){return{children:Gr(n,r[1],e),target:Zr(r[2]),title:Zr(r[3])}},_:function(r,n,e){return X("a",{key:e.key,href:Q(r.target,"a","href"),title:r.title},n(r.children,e))}},u[a.linkAngleBraceStyleDetector]={u:["<"],i:Ir(D),o:0,l:function(r){return{children:[{text:r[1],type:a.text}],target:r[1],type:a.link}}},u[a.linkBareUrlDetector]={u:function(r,e){return!e.inAnchor&&!n.disableAutoLink&&(jr(r,"http://")||jr(r,"https://"))},i:Ir(M),o:0,l:function(r){return{children:[{text:r[1],type:a.text}],target:r[1],title:void 0,type:a.link}}},u[a.linkMailtoDetector]={u:["<"],i:Ir(w),o:0,l:function(r){var n=r[1],e=r[1];return d.test(e)||(e="mailto:"+e),{children:[{text:n.replace("mailto:",""),type:a.text}],target:e,type:a.link}}},u[a.orderedList]=Sr(X,1),u[a.unorderedList]=Sr(X,2),u[a.newlineCoalescer]={i:wr(x),o:3,l:Hr,_:function(){return"\n"}},u[a.paragraph]={i:Cr(function(r,n){if(n.inline||n.simple||n.inHTML&&-1===r.indexOf("\n\n")&&-1===n.prevCapture.indexOf("\n\n"))return null;var e="";r.split("\n").every(function(r){return r+="\n",!J.some(function(n){return n.test(r)})&&(e+=r,!!r.trim())});var t=Er(e);return""===t?null:[e,,t]}),o:3,l:Vr,_:function(r,n,e){return X("p",{key:e.key},n(r.children,e))}},u[a.ref]={u:["["],i:Ir(N),o:0,l:function(r){return dr[r[1]]={target:r[2],title:r[4]},{}},_:Qr},u[a.refImage]={u:["!["],i:Mr(G),o:0,l:function(r){return{alt:r[1]?Zr(r[1]):void 0,ref:r[2]}},_:function(r,n,e){return dr[r.ref]?X("img",{key:e.key,alt:r.alt,src:Q(dr[r.ref].target,"img","src"),title:dr[r.ref].title}):null}},u[a.refLink]={u:function(r){return"["===r[0]&&-1===r.indexOf("](")},i:Ir(U),o:0,l:function(r,n,e){return{children:n(r[1],e),fallbackChildren:r[0],ref:r[2]}},_:function(r,n,e){return dr[r.ref]?X("a",{key:e.key,href:Q(dr[r.ref].target,"a","href"),title:dr[r.ref].title},n(r.children,e)):X("span",{key:e.key},r.fallbackChildren)}},u[a.table]={u:["|"],i:wr(P),o:1,l:Br,_:function(r,n,e){var t=r;return X("table",{key:e.key},X("thead",null,X("tr",null,t.header.map(function(r,u){return X("th",{key:u,style:Lr(t,u)},n(r,e))}))),X("tbody",null,t.cells.map(function(r,u){return X("tr",{key:u},r.map(function(r,u){return X("td",{key:u,style:Lr(t,u)},n(r,e))}))})))}},u[a.text]={i:Cr(function(r,n){var e;return jr(r,":")&&(e=tr.exec(r)),e||ir.exec(r)}),o:4,l:function(r){var e=r[0];return{text:-1===e.indexOf("&")?e:e.replace(T,function(r,e){return n.namedCodesToUnicode[e]||r})}},_:function(r){return r.text}},u[a.textBolded]={u:["**","__"],i:Mr(Y),o:2,l:function(r,n,e){return{children:n(r[2],e)}},_:function(r,n,e){return X("strong",{key:e.key},n(r.children,e))}},u[a.textEmphasized]={u:function(r){var n=r[0];return("*"===n||"_"===n)&&r[1]!==n},i:Mr(rr),o:3,l:function(r,n,e){return{children:n(r[2],e)}},_:function(r,n,e){return X("em",{key:e.key},n(r.children,e))}},u[a.textEscaped]={u:["\\"],i:Mr(ur),o:1,l:function(r){return{text:r[1],type:a.text}}},u[a.textMarked]={u:["=="],i:Mr(nr),o:3,l:Vr,_:function(r,n,e){return X("mark",{key:e.key},n(r.children,e))}},u[a.textStrikethroughed]={u:["~~"],i:Mr(er),o:3,l:Vr,_:function(r,n,e){return X("del",{key:e.key},n(r.children,e))}},u);!0===n.disableParsingRawHTML&&(delete _r[a.htmlBlock],delete _r[a.htmlSelfClosing]);var sr,pr=function(r){var n=Object.keys(r);function e(t,u){var a=[];if(u.prevCapture=u.prevCapture||"",t.trim())for(;t;)for(var i=0;i<n.length;){var o=n[i],c=r[o];if(!c.u||Or(t,u,c.u)){var f=c.i(t,u);if(f&&f[0]){t=t.substring(f[0].length);var l=c.l(f,e,u);u.prevCapture+=f[0],l.type||(l.type=o),a.push(l);break}i++}else i++}return u.prevCapture="",a}return n.sort(function(n,e){return r[n].o-r[e].o||(n<e?-1:1)}),function(r,n){return e(function(r){return r.replace(b,"\n").replace(z,"").replace(H,"    ")}(r),n)}}(_r),vr=(sr=function(r,n){return function(e,t,u){var a=r[e.type]._;return n?n(function(){return a(e,t,u)},e,t,u):a(e,t,u)}}(_r,n.renderRule),function r(n,e){if(void 0===e&&(e={}),Array.isArray(n)){for(var t=e.key,u=[],a=!1,i=0;i<n.length;i++){e.key=i;var o=r(n[i],e),c="string"==typeof o;c&&a?u[u.length-1]+=o:null!==o&&u.push(o),a=c}return e.key=t,u}return sr(n,r,e)}),yr=ar(r);return lr.length?X("div",null,yr,X("footer",{key:"footer"},lr.map(function(r){return X("div",{id:_(r.identifier,Rr),key:r.identifier},r.identifier,vr(pr(r.footnote,{inline:!0})))}))):yr}var Yr=function(r){var n=r.children,t=void 0===n?"":n,a=r.options,i=function(r,n){if(null==r)return{};var e,t,u={},a=Object.keys(r);for(t=0;t<a.length;t++)n.indexOf(e=a[t])>=0||(u[e]=r[e]);return u}(r,u);return e.cloneElement(Xr(t,a),i)};return Object.assign(Yr,{compiler:Xr,RuleType:a}),Yr});
//# sourceMappingURL=index.umd.js.map
