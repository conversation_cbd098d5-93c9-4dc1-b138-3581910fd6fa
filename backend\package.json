{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "markdown-to-jsx": "^7.7.12", "mongoose": "^8.16.3", "morgan": "^1.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}