function r(r){if(r&&r.t)return r;var n=Object.create(null);return r&&Object.keys(r).forEach(function(e){if("default"!==e){var t=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(n,e,t.get?t:{enumerable:!0,get:function(){return r[e]}})}}),n.default=r,n}var n=/*#__PURE__*/r(require("react"));function e(){return e=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},e.apply(this,arguments)}var t=["children","options"],u={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},a=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(r,n){return r[n.toLowerCase()]=n,r},{class:"className",for:"htmlFor"}),i={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},o=["style","script","pre"],c=["src","href","data","formAction","srcDoc","action"],f=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,l=/mailto:/i,_=/\n{2,}$/,d=/^(\s*>[\s\S]*?)(?=\n\n|$)/,s=/^ *> ?/gm,v=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,p=/^ {2,}\n/,y=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,h=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,g=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,m=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,k=/^(?:\n *)*\n/,x=/\r\n?/g,q=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,b=/^\[\^([^\]]+)]/,S=/\f/g,z=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,$=/^\s*?\[(x|\s)\]/,E=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,O=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,R=/^([^\n]+)\n *(=|-){3,} *\n/,j=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,A=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,B=/^<!--[\s\S]*?(?:-->)/,L=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,T=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,C=/^\{.*\}$/,I=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,M=/^<([^ >]+@[^ >]+)>/,w=/^<([^ >]+:\/[^ >]+)>/,D=/-([a-z])?/gi,F=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,P=/^[^\n]+(?:  \n|\n{2,})/,Z=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,N=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,G=/^\[([^\]]*)\] ?\[([^\]]*)\]/,U=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,V=/\t/g,H=/(^ *\||\| *$)/g,Q=/^ *:-+: *$/,W=/^ *:-+ *$/,J=/^ *-+: *$/,K="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",X=RegExp("^([*_])\\1"+K+"\\1\\1(?!\\1)"),Y=RegExp("^([*_])"+K+"\\1(?!\\1)"),rr=RegExp("^(==)"+K+"\\1"),nr=RegExp("^(~~)"+K+"\\1"),er=/^(:[a-zA-Z0-9-_]+:)/,tr=/^\\([^0-9A-Za-z\s])/,ur=/\\([^0-9A-Za-z\s])/g,ar=/^[\s\S](?:(?!  \n|[0-9]\.|http)[^=*_~\-\n:<`\\\[!])*/,ir=/^\n+/,or=/^([ \t]*)/,cr=/(?:^|\n)( *)$/,fr="(?:\\d+\\.)",lr="(?:[*+-])";function _r(r){return"( *)("+(1===r?fr:lr)+") +"}var dr=_r(1),sr=_r(2);function vr(r){return RegExp("^"+(1===r?dr:sr))}var pr=vr(1),yr=vr(2);function hr(r){return RegExp("^"+(1===r?dr:sr)+"[^\\n]*(?:\\n(?!\\1"+(1===r?fr:lr)+" )[^\\n]*)*(\\n|$)","gm")}var gr=hr(1),mr=hr(2);function kr(r){var n=1===r?fr:lr;return RegExp("^( *)("+n+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+n+" (?!"+n+" ))\\n*|\\s*\\n*$)")}var xr=kr(1),qr=kr(2);function br(r,n){var e=1===n,t=e?xr:qr,a=e?gr:mr,i=e?pr:yr;return{u:function(r){return i.test(r)},i:Tr(function(r,n){var e=cr.exec(n.prevCapture);return e&&(n.list||!n.inline&&!n.simple)?t.exec(r=e[1]+r):null}),o:1,l:function(r,n,t){var u=e?+r[2]:void 0,o=r[0].replace(_,"\n").match(a),c=!1;return{items:o.map(function(r,e){var u=i.exec(r)[0].length,a=RegExp("^ {1,"+u+"}","gm"),f=r.replace(a,"").replace(i,""),l=e===o.length-1,_=-1!==f.indexOf("\n\n")||l&&c;c=_;var d,s=t.inline,v=t.list;t.list=!0,_?(t.inline=!1,d=$r(f)+"\n\n"):(t.inline=!0,d=$r(f));var p=n(d,t);return t.inline=s,t.list=v,p}),ordered:e,start:u}},_:function(n,e,t){return r(n.ordered?"ol":"ul",{key:t.key,start:n.type===u.orderedList?n.start:void 0},n.items.map(function(n,u){return r("li",{key:u},e(n,t))}))}}}var Sr=RegExp("^\\[((?:\\[[^\\[\\]]*(?:\\[[^\\[\\]]*\\][^\\[\\]]*)*\\]|[^\\[\\]])*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),zr=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;function $r(r){for(var n=r.length;n>0&&r[n-1]<=" ";)n--;return r.slice(0,n)}function Er(r,n){return r.startsWith(n)}function Or(r,n,e){if(Array.isArray(e)){for(var t=0;t<e.length;t++)if(Er(r,e[t]))return!0;return!1}return e(r,n)}function Rr(r){return r.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function jr(r){return J.test(r)?"right":Q.test(r)?"center":W.test(r)?"left":null}function Ar(r,n,e,t){var u=e.inTable;e.inTable=!0;var a=[[]],i="";function o(){if(i){var r=a[a.length-1];r.push.apply(r,n(i,e)),i=""}}return r.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(r,n,e){"|"===r.trim()&&(o(),t)?0!==n&&n!==e.length-1&&a.push([]):i+=r}),o(),e.inTable=u,a}function Br(r,n,e){e.inline=!0;var t=r[2]?r[2].replace(H,"").split("|").map(jr):[],a=r[3]?function(r,n,e){return r.trim().split("\n").map(function(r){return Ar(r,n,e,!0)})}(r[3],n,e):[],i=Ar(r[1],n,e,!!a.length);return e.inline=!1,a.length?{align:t,cells:a,header:i,type:u.table}:{children:i,type:u.paragraph}}function Lr(r,n){return null==r.align[n]?{}:{textAlign:r.align[n]}}function Tr(r){return r.inline=1,r}function Cr(r){return Tr(function(n,e){return e.inline?r.exec(n):null})}function Ir(r){return Tr(function(n,e){return e.inline||e.simple?r.exec(n):null})}function Mr(r){return function(n,e){return e.inline||e.simple?null:r.exec(n)}}function wr(r){return Tr(function(n){return r.exec(n)})}var Dr=/(javascript|vbscript|data(?!:image)):/i;function Fr(r){try{var n=decodeURIComponent(r).replace(/[^A-Za-z0-9/:]/g,"");if(Dr.test(n))return null}catch(r){return null}return r}function Pr(r){return r?r.replace(ur,"$1"):r}function Zr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!0,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Nr(r,n,e){var t=e.inline||!1,u=e.simple||!1;e.inline=!1,e.simple=!0;var a=r(n,e);return e.inline=t,e.simple=u,a}function Gr(r,n,e){var t=e.inline||!1;e.inline=!1;var u=r(n,e);return e.inline=t,u}var Ur=function(r,n,e){return{children:Zr(n,r[2],e)}};function Vr(){return{}}function Hr(){return null}function Qr(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Wr(r,n,e){for(var t=r,u=n.split(".");u.length&&void 0!==(t=t[u[0]]);)u.shift();return t||e}function Jr(r,n){var e=Wr(n,r);return e?"function"==typeof e||"object"==typeof e&&"render"in e?e:Wr(n,r+".component",r):r}function Kr(r,t){var _;void 0===r&&(r=""),void 0===t&&(t={}),t.overrides=t.overrides||{},t.namedCodesToUnicode=t.namedCodesToUnicode?e({},i,t.namedCodesToUnicode):i;var H=t.slugify||Rr,Q=t.sanitizer||Fr,W=t.createElement||n.createElement,J=[d,h,g,t.enforceAtxHeadings?O:E,R,F,xr,qr],K=[].concat(J,[P,j,B,T]);function ur(r,n){var u=Wr(t.overrides,r+".props",{});return W.apply(void 0,[Jr(r,t.overrides),e({},n,u,{className:Qr(null==n?void 0:n.className,u.className)||void 0})].concat([].slice.call(arguments,2)))}function cr(r){r=r.replace(z,"");var n=!1;t.forceInline?n=!0:t.forceBlock||(n=!1===U.test(r));for(var e=pr(vr(n?r:$r(r).replace(ir,"")+"\n\n",{inline:n}));"string"==typeof e[e.length-1]&&!e[e.length-1].trim();)e.pop();if(null===t.wrapper)return e;var u,a=t.wrapper||(n?"span":"div");if(e.length>1||t.forceWrapper)u=e;else{if(1===e.length)return"string"==typeof(u=e[0])?ur("span",{key:"outer"},u):u;u=null}return W(a,{key:"outer"},u)}function fr(r,n){if(!n||!n.trim())return null;var e=n.match(f);return e?e.reduce(function(n,e){var t=e.indexOf("=");if(-1!==t){var u=function(r){return-1!==r.indexOf("-")&&null===r.match(L)&&(r=r.replace(D,function(r,n){return n.toUpperCase()})),r}(e.slice(0,t)).trim(),i=function(r){var n=r[0];return('"'===n||"'"===n)&&r.length>=2&&r[r.length-1]===n?r.slice(1,-1):r}(e.slice(t+1).trim()),o=a[u]||u;if("ref"===o)return n;var f=n[o]=function(r,n,e,t){return"style"===n?function(r){var n=[],e="",t=!1,u=!1,a="";if(!r)return n;for(var i=0;i<r.length;i++){var o=r[i];if('"'!==o&&"'"!==o||t||(u?o===a&&(u=!1,a=""):(u=!0,a=o)),"("===o&&e.endsWith("url")?t=!0:")"===o&&t&&(t=!1),";"!==o||u||t)e+=o;else{var c=e.trim();if(c){var f=c.indexOf(":");if(f>0){var l=c.slice(0,f).trim(),_=c.slice(f+1).trim();n.push([l,_])}}e=""}}var d=e.trim();if(d){var s=d.indexOf(":");if(s>0){var v=d.slice(0,s).trim(),p=d.slice(s+1).trim();n.push([v,p])}}return n}(e).reduce(function(n,e){var u=e[0],a=e[1];return n[u.replace(/(-[a-z])/g,function(r){return r[1].toUpperCase()})]=t(a,r,u),n},{}):-1!==c.indexOf(n)?t(Pr(e),r,n):(e.match(C)&&(e=Pr(e.slice(1,e.length-1))),"true"===e||"false"!==e&&e)}(r,u,i,Q);"string"==typeof f&&(j.test(f)||T.test(f))&&(n[o]=cr(f.trim()))}else"style"!==e&&(n[a[e]||e]=!0);return n},{}):null}var lr=[],_r={},dr=((_={})[u.blockQuote]={u:[">"],i:Mr(d),o:1,l:function(r,n,e){var t=r[0].replace(s,"").match(v);return{alert:t[1],children:n(t[2],e)}},_:function(r,n,e){var t={key:e.key};return r.alert&&(t.className="markdown-alert-"+H(r.alert.toLowerCase(),Rr),r.children.unshift({attrs:{},children:[{type:u.text,text:r.alert}],noInnerParse:!0,type:u.htmlBlock,tag:"header"})),ur("blockquote",t,n(r.children,e))}},_[u.breakLine]={i:wr(p),o:1,l:Vr,_:function(r,n,e){return ur("br",{key:e.key})}},_[u.breakThematic]={u:function(r){var n=r[0];return"-"===n||"*"===n||"_"===n},i:Mr(y),o:1,l:Vr,_:function(r,n,e){return ur("hr",{key:e.key})}},_[u.codeBlock]={u:["    "],i:Mr(g),o:0,l:function(r){return{lang:void 0,text:Pr($r(r[0].replace(/^ {4}/gm,"")))}},_:function(r,n,t){return ur("pre",{key:t.key},ur("code",e({},r.attrs,{className:r.lang?"lang-"+r.lang:""}),r.text))}},_[u.codeFenced]={u:["```","~~~"],i:Mr(h),o:0,l:function(r){return{attrs:fr("code",r[3]||""),lang:r[2]||void 0,text:r[4],type:u.codeBlock}}},_[u.codeInline]={u:["`"],i:Ir(m),o:3,l:function(r){return{text:Pr(r[2])}},_:function(r,n,e){return ur("code",{key:e.key},r.text)}},_[u.footnote]={u:["[^"],i:Mr(q),o:0,l:function(r){return lr.push({footnote:r[2],identifier:r[1]}),{}},_:Hr},_[u.footnoteReference]={u:["[^"],i:Cr(b),o:1,l:function(r){return{target:"#"+H(r[1],Rr),text:r[1]}},_:function(r,n,e){return ur("a",{key:e.key,href:Q(r.target,"a","href")},ur("sup",{key:e.key},r.text))}},_[u.gfmTask]={u:["[ ]","[x]"],i:Cr($),o:1,l:function(r){return{completed:"x"===r[1].toLowerCase()}},_:function(r,n,e){return ur("input",{checked:r.completed,key:e.key,readOnly:!0,type:"checkbox"})}},_[u.heading]={u:["#"],i:Mr(t.enforceAtxHeadings?O:E),o:1,l:function(r,n,e){return{children:Zr(n,r[2],e),id:H(r[2],Rr),level:r[1].length}},_:function(r,n,e){return ur("h"+r.level,{id:r.id,key:e.key},n(r.children,e))}},_[u.headingSetext]={i:Mr(R),o:0,l:function(r,n,e){return{children:Zr(n,r[1],e),level:"="===r[2]?1:2,type:u.heading}}},_[u.htmlBlock]={u:["<"],i:wr(j),o:1,l:function(r,n,e){var t,u=r[3].match(or),a=RegExp("^"+u[1],"gm"),i=r[3].replace(a,""),c=(t=i,K.some(function(r){return r.test(t)})?Gr:Zr),f=r[1].toLowerCase(),l=-1!==o.indexOf(f),_=(l?f:r[1]).trim(),d={attrs:fr(_,r[2]),noInnerParse:l,tag:_};if(e.inAnchor=e.inAnchor||"a"===f,l)d.text=r[3];else{var s=e.inHTML;e.inHTML=!0,d.children=c(n,i,e),e.inHTML=s}return e.inAnchor=!1,d},_:function(r,n,t){return ur(r.tag,e({key:t.key},r.attrs),r.text||(r.children?n(r.children,t):""))}},_[u.htmlSelfClosing]={u:["<"],i:wr(T),o:1,l:function(r){var n=r[1].trim();return{attrs:fr(n,r[2]||""),tag:n}},_:function(r,n,t){return ur(r.tag,e({},r.attrs,{key:t.key}))}},_[u.htmlComment]={u:["\x3c!--"],i:wr(B),o:1,l:function(){return{}},_:Hr},_[u.image]={u:["!["],i:Ir(zr),o:1,l:function(r){return{alt:Pr(r[1]),target:Pr(r[2]),title:Pr(r[3])}},_:function(r,n,e){return ur("img",{key:e.key,alt:r.alt||void 0,title:r.title||void 0,src:Q(r.target,"img","src")})}},_[u.link]={u:["["],i:Cr(Sr),o:3,l:function(r,n,e){return{children:Nr(n,r[1],e),target:Pr(r[2]),title:Pr(r[3])}},_:function(r,n,e){return ur("a",{key:e.key,href:Q(r.target,"a","href"),title:r.title},n(r.children,e))}},_[u.linkAngleBraceStyleDetector]={u:["<"],i:Cr(w),o:0,l:function(r){return{children:[{text:r[1],type:u.text}],target:r[1],type:u.link}}},_[u.linkBareUrlDetector]={u:function(r,n){return!n.inAnchor&&!t.disableAutoLink&&(Er(r,"http://")||Er(r,"https://"))},i:Cr(I),o:0,l:function(r){return{children:[{text:r[1],type:u.text}],target:r[1],title:void 0,type:u.link}}},_[u.linkMailtoDetector]={u:["<"],i:Cr(M),o:0,l:function(r){var n=r[1],e=r[1];return l.test(e)||(e="mailto:"+e),{children:[{text:n.replace("mailto:",""),type:u.text}],target:e,type:u.link}}},_[u.orderedList]=br(ur,1),_[u.unorderedList]=br(ur,2),_[u.newlineCoalescer]={i:Mr(k),o:3,l:Vr,_:function(){return"\n"}},_[u.paragraph]={i:Tr(function(r,n){if(n.inline||n.simple||n.inHTML&&-1===r.indexOf("\n\n")&&-1===n.prevCapture.indexOf("\n\n"))return null;var e="";r.split("\n").every(function(r){return r+="\n",!J.some(function(n){return n.test(r)})&&(e+=r,!!r.trim())});var t=$r(e);return""===t?null:[e,,t]}),o:3,l:Ur,_:function(r,n,e){return ur("p",{key:e.key},n(r.children,e))}},_[u.ref]={u:["["],i:Cr(Z),o:0,l:function(r){return _r[r[1]]={target:r[2],title:r[4]},{}},_:Hr},_[u.refImage]={u:["!["],i:Ir(N),o:0,l:function(r){return{alt:r[1]?Pr(r[1]):void 0,ref:r[2]}},_:function(r,n,e){return _r[r.ref]?ur("img",{key:e.key,alt:r.alt,src:Q(_r[r.ref].target,"img","src"),title:_r[r.ref].title}):null}},_[u.refLink]={u:function(r){return"["===r[0]&&-1===r.indexOf("](")},i:Cr(G),o:0,l:function(r,n,e){return{children:n(r[1],e),fallbackChildren:r[0],ref:r[2]}},_:function(r,n,e){return _r[r.ref]?ur("a",{key:e.key,href:Q(_r[r.ref].target,"a","href"),title:_r[r.ref].title},n(r.children,e)):ur("span",{key:e.key},r.fallbackChildren)}},_[u.table]={u:["|"],i:Mr(F),o:1,l:Br,_:function(r,n,e){var t=r;return ur("table",{key:e.key},ur("thead",null,ur("tr",null,t.header.map(function(r,u){return ur("th",{key:u,style:Lr(t,u)},n(r,e))}))),ur("tbody",null,t.cells.map(function(r,u){return ur("tr",{key:u},r.map(function(r,u){return ur("td",{key:u,style:Lr(t,u)},n(r,e))}))})))}},_[u.text]={i:Tr(function(r,n){var e;return Er(r,":")&&(e=er.exec(r)),e||ar.exec(r)}),o:4,l:function(r){var n=r[0];return{text:-1===n.indexOf("&")?n:n.replace(A,function(r,n){return t.namedCodesToUnicode[n]||r})}},_:function(r){return r.text}},_[u.textBolded]={u:["**","__"],i:Ir(X),o:2,l:function(r,n,e){return{children:n(r[2],e)}},_:function(r,n,e){return ur("strong",{key:e.key},n(r.children,e))}},_[u.textEmphasized]={u:function(r){var n=r[0];return("*"===n||"_"===n)&&r[1]!==n},i:Ir(Y),o:3,l:function(r,n,e){return{children:n(r[2],e)}},_:function(r,n,e){return ur("em",{key:e.key},n(r.children,e))}},_[u.textEscaped]={u:["\\"],i:Ir(tr),o:1,l:function(r){return{text:r[1],type:u.text}}},_[u.textMarked]={u:["=="],i:Ir(rr),o:3,l:Ur,_:function(r,n,e){return ur("mark",{key:e.key},n(r.children,e))}},_[u.textStrikethroughed]={u:["~~"],i:Ir(nr),o:3,l:Ur,_:function(r,n,e){return ur("del",{key:e.key},n(r.children,e))}},_);!0===t.disableParsingRawHTML&&(delete dr[u.htmlBlock],delete dr[u.htmlSelfClosing]);var sr,vr=function(r){var n=Object.keys(r);function e(t,u){var a=[];if(u.prevCapture=u.prevCapture||"",t.trim())for(;t;)for(var i=0;i<n.length;){var o=n[i],c=r[o];if(!c.u||Or(t,u,c.u)){var f=c.i(t,u);if(f&&f[0]){t=t.substring(f[0].length);var l=c.l(f,e,u);u.prevCapture+=f[0],l.type||(l.type=o),a.push(l);break}i++}else i++}return u.prevCapture="",a}return n.sort(function(n,e){return r[n].o-r[e].o||(n<e?-1:1)}),function(r,n){return e(function(r){return r.replace(x,"\n").replace(S,"").replace(V,"    ")}(r),n)}}(dr),pr=(sr=function(r,n){return function(e,t,u){var a=r[e.type]._;return n?n(function(){return a(e,t,u)},e,t,u):a(e,t,u)}}(dr,t.renderRule),function r(n,e){if(void 0===e&&(e={}),Array.isArray(n)){for(var t=e.key,u=[],a=!1,i=0;i<n.length;i++){e.key=i;var o=r(n[i],e),c="string"==typeof o;c&&a?u[u.length-1]+=o:null!==o&&u.push(o),a=c}return e.key=t,u}return sr(n,r,e)}),yr=cr(r);return lr.length?ur("div",null,yr,ur("footer",{key:"footer"},lr.map(function(r){return ur("div",{id:H(r.identifier,Rr),key:r.identifier},r.identifier,pr(vr(r.footnote,{inline:!0})))}))):yr}var Xr=function(r){var e=r.children,u=void 0===e?"":e,a=r.options,i=function(r,n){if(null==r)return{};var e,t,u={},a=Object.keys(r);for(t=0;t<a.length;t++)n.indexOf(e=a[t])>=0||(u[e]=r[e]);return u}(r,t);return n.cloneElement(Kr(u,a),i)};Object.assign(Xr,{compiler:Kr,RuleType:u}),module.exports=Xr;
//# sourceMappingURL=index.cjs.map
